{"name": "stdio_test", "description": "stdio_test", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750020115983_query": {"node_id": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750020115983", "node_name": "Tavily Web Search and Extraction Server - tavily-search", "input_name": "query", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_DesktopCommanderMCP_create_directory-1750043585945_path": {"node_id": "MCP_DesktopCommanderMCP_create_directory-1750043585945", "node_name": "DesktopCommanderMCP - create_directory", "input_name": "path", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_DesktopCommanderMCP_edit_block-1750043592293_file_path": {"node_id": "MCP_DesktopCommanderMCP_edit_block-1750043592293", "node_name": "DesktopCommanderMCP - edit_block", "input_name": "file_path", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_DesktopCommanderMCP_edit_block-1750043592293_old_string": {"node_id": "MCP_DesktopCommanderMCP_edit_block-1750043592293", "node_name": "DesktopCommanderMCP - edit_block", "input_name": "old_string", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_DesktopCommanderMCP_edit_block-1750043592293_new_string": {"node_id": "MCP_DesktopCommanderMCP_edit_block-1750043592293", "node_name": "DesktopCommanderMCP - edit_block", "input_name": "new_string", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750020115983", "type": "WorkflowNode", "position": {"x": 460, "y": 140}, "data": {"label": "Tavily Web Search and Extraction Server - tavily-search", "type": "mcp", "originalType": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search", "definition": {"name": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search", "display_name": "Tavily Web Search and Extraction Server - tavily-search", "description": "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "query", "display_name": "query", "info": "Search query", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "search_depth", "display_name": "search depth", "info": "The depth of the search. It can be 'basic' or 'advanced'", "input_type": "dropdown", "input_types": ["dropdown", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "basic", "options": ["basic", "advanced"], "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "topic", "display_name": "topic", "info": "The category of the search. This will determine which of our agents will be used for the search", "input_type": "dropdown", "input_types": ["dropdown", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "general", "options": ["general", "news"], "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "days", "display_name": "days", "info": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 3, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "time_range", "display_name": "time range", "info": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "input_type": "dropdown", "input_types": ["dropdown", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": ["day", "week", "month", "year", "d", "w", "m", "y"], "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "max_results", "display_name": "max results", "info": "The maximum number of search results to return", "input_type": "number", "input_types": ["number", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"min": 5, "max": 20}}, {"name": "include_images", "display_name": "include images", "info": "Include a list of query-related images in the response", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "include_image_descriptions", "display_name": "include image descriptions", "info": "Include a list of query-related images and their descriptions in the response", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "include_raw_content", "display_name": "include raw content", "info": "Include the cleaned and parsed HTML content of each search result", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "include_domains", "display_name": "include domains", "info": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "exclude_domains", "display_name": "exclude domains", "info": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "Generated_String", "display_name": "Generated_String", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_tavily_web_search_and_extraction_server_tavily-search", "type": "MCP", "mcp_info": {"server_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "server_path": "", "tool_name": "tavily-search", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "search_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "The depth of the search. It can be 'basic' or 'advanced'", "default": "basic"}, "topic": {"type": "string", "enum": ["general", "news"], "description": "The category of the search. This will determine which of our agents will be used for the search", "default": "general"}, "days": {"type": "number", "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "default": 3}, "time_range": {"type": "string", "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "enum": ["day", "week", "month", "year", "d", "w", "m", "y"]}, "max_results": {"type": "number", "description": "The maximum number of search results to return", "default": 10, "minimum": 5, "maximum": 20}, "include_images": {"type": "boolean", "description": "Include a list of query-related images in the response", "default": false}, "include_image_descriptions": {"type": "boolean", "description": "Include a list of query-related images and their descriptions in the response", "default": false}, "include_raw_content": {"type": "boolean", "description": "Include the cleaned and parsed HTML content of each search result", "default": false}, "include_domains": {"type": "array", "items": {"type": "string"}, "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "default": []}, "exclude_domains": {"type": "array", "items": {"type": "string"}, "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "default": []}}, "required": ["query"]}, "output_schema": {"properties": {"Generated_String": {"type": "string", "description": "generated string from tavily", "title": "Generated_String"}}}}}, "config": {"search_depth": "basic", "topic": "general", "days": 3, "max_results": 10, "include_domains": [], "exclude_domains": []}}, "width": 208, "height": 424, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-1750043578812", "type": "WorkflowNode", "position": {"x": 1000, "y": -180}, "data": {"label": "AI Agent Executor", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "input_type": "credential", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"tool_connections": {"tools": [{"node_id": "MCP_DesktopCommanderMCP_create_directory-1750043585945", "node_type": "MCP_DesktopCommanderMCP_create_directory", "node_label": "DesktopCommanderMCP - create_directory", "component_id": "MCP_DesktopCommanderMCP_create_directory-1750043585945", "component_type": "MCP_DesktopCommanderMCP_create_directory", "component_name": "DesktopCommanderMCP - create_directory", "component_definition": {"name": "MCP_DesktopCommanderMCP_create_directory", "display_name": "DesktopCommanderMCP - create_directory", "description": "\n                        Create a new directory or ensure a directory exists.\n                        \n                        Can create multiple nested directories in one operation.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "path", "display_name": "path", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "is_valid": true, "path": "mcp.mcp_desktopcommandermcp_create_directory", "type": "MCP", "mcp_info": {"server_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "server_path": "", "tool_name": "create_directory", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null}}, "component_config": {}, "mcp_metadata": {"server_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "server_path": "", "tool_name": "create_directory", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null}}, {"node_id": "MCP_DesktopCommanderMCP_edit_block-1750043592293", "node_type": "MCP_DesktopCommanderMCP_edit_block", "node_label": "DesktopCommanderMCP - edit_block", "component_id": "MCP_DesktopCommanderMCP_edit_block-1750043592293", "component_type": "MCP_DesktopCommanderMCP_edit_block", "component_name": "DesktopCommanderMCP - edit_block", "component_definition": {"name": "MCP_DesktopCommanderMCP_edit_block", "display_name": "DesktopCommanderMCP - edit_block", "description": "\n                        Apply surgical text replacements to files.\n                        \n                        BEST PRACTICE: Make multiple small, focused edits rather than one large edit.\n                        Each edit_block call should change only what needs to be changed - include just enough \n                        context to uniquely identify the text being modified.\n                        \n                        Takes:\n                        - file_path: Path to the file to edit\n                        - old_string: Text to replace\n                        - new_string: Replacement text\n                        - expected_replacements: Optional parameter for number of replacements\n                        \n                        By default, replaces only ONE occurrence of the search text.\n                        To replace multiple occurrences, provide the expected_replacements parameter with\n                        the exact number of matches expected.\n                        \n                        UNIQUENESS REQUIREMENT: When expected_replacements=1 (default), include the minimal\n                        amount of context necessary (typically 1-3 lines) before and after the change point,\n                        with exact whitespace and indentation.\n                        \n                        When editing multiple sections, make separate edit_block calls for each distinct change\n                        rather than one large replacement.\n                        \n                        When a close but non-exact match is found, a character-level diff is shown in the format:\n                        common_prefix{-removed-}{+added+}common_suffix to help you identify what's different.\n                        \n                        Similar to write_file, there is a configurable line limit (fileWriteLineLimit) that warns\n                        if the edited file exceeds this limit. If this happens, consider breaking your edits into\n                        smaller, more focused changes.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "file_path", "display_name": "file path", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "old_string", "display_name": "old string", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "new_string", "display_name": "new string", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "expected_replacements", "display_name": "expected replacements", "info": "", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "is_valid": true, "path": "mcp.mcp_desktopcommandermcp_edit_block", "type": "MCP", "mcp_info": {"server_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "server_path": "", "tool_name": "edit_block", "input_schema": {"type": "object", "properties": {"file_path": {"type": "string"}, "old_string": {"type": "string"}, "new_string": {"type": "string"}, "expected_replacements": {"type": "number", "default": 1}}, "required": ["file_path", "old_string", "new_string"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null}}, "component_config": {}, "mcp_metadata": {"server_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "server_path": "", "tool_name": "edit_block", "input_schema": {"type": "object", "properties": {"file_path": {"type": "string"}, "old_string": {"type": "string"}, "new_string": {"type": "string"}, "expected_replacements": {"type": "number", "default": 1}}, "required": ["file_path", "old_string", "new_string"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null}}]}}}, "style": {"opacity": 1}, "width": 208, "height": 218, "selected": false, "positionAbsolute": {"x": 1000, "y": -180}, "dragging": false}, {"id": "MCP_DesktopCommanderMCP_create_directory-1750043585945", "type": "WorkflowNode", "position": {"x": 480, "y": -260}, "data": {"label": "DesktopCommanderMCP - create_directory", "type": "mcp", "originalType": "MCP_DesktopCommanderMCP_create_directory", "definition": {"name": "MCP_DesktopCommanderMCP_create_directory", "display_name": "DesktopCommanderMCP - create_directory", "description": "\n                        Create a new directory or ensure a directory exists.\n                        \n                        Can create multiple nested directories in one operation.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "path", "display_name": "path", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "is_valid": true, "path": "mcp.mcp_desktopcommandermcp_create_directory", "type": "MCP", "mcp_info": {"server_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "server_path": "", "tool_name": "create_directory", "input_schema": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null}}, "config": {}}, "style": {"opacity": 1}, "width": 210, "height": 122, "selected": false, "positionAbsolute": {"x": 480, "y": -260}, "dragging": false}, {"id": "MCP_DesktopCommanderMCP_edit_block-1750043592293", "type": "WorkflowNode", "position": {"x": 560, "y": -500}, "data": {"label": "DesktopCommanderMCP - edit_block", "type": "mcp", "originalType": "MCP_DesktopCommanderMCP_edit_block", "definition": {"name": "MCP_DesktopCommanderMCP_edit_block", "display_name": "DesktopCommanderMCP - edit_block", "description": "\n                        Apply surgical text replacements to files.\n                        \n                        BEST PRACTICE: Make multiple small, focused edits rather than one large edit.\n                        Each edit_block call should change only what needs to be changed - include just enough \n                        context to uniquely identify the text being modified.\n                        \n                        Takes:\n                        - file_path: Path to the file to edit\n                        - old_string: Text to replace\n                        - new_string: Replacement text\n                        - expected_replacements: Optional parameter for number of replacements\n                        \n                        By default, replaces only ONE occurrence of the search text.\n                        To replace multiple occurrences, provide the expected_replacements parameter with\n                        the exact number of matches expected.\n                        \n                        UNIQUENESS REQUIREMENT: When expected_replacements=1 (default), include the minimal\n                        amount of context necessary (typically 1-3 lines) before and after the change point,\n                        with exact whitespace and indentation.\n                        \n                        When editing multiple sections, make separate edit_block calls for each distinct change\n                        rather than one large replacement.\n                        \n                        When a close but non-exact match is found, a character-level diff is shown in the format:\n                        common_prefix{-removed-}{+added+}common_suffix to help you identify what's different.\n                        \n                        Similar to write_file, there is a configurable line limit (fileWriteLineLimit) that warns\n                        if the edited file exceeds this limit. If this happens, consider breaking your edits into\n                        smaller, more focused changes.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "file_path", "display_name": "file path", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "old_string", "display_name": "old string", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "new_string", "display_name": "new string", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "expected_replacements", "display_name": "expected replacements", "info": "", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "is_valid": true, "path": "mcp.mcp_desktopcommandermcp_edit_block", "type": "MCP", "mcp_info": {"server_id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "server_path": "", "tool_name": "edit_block", "input_schema": {"type": "object", "properties": {"file_path": {"type": "string"}, "old_string": {"type": "string"}, "new_string": {"type": "string"}, "expected_replacements": {"type": "number", "default": 1}}, "required": ["file_path", "old_string", "new_string"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null}}, "config": {}}, "style": {"opacity": 1}, "width": 210, "height": 218, "selected": false, "positionAbsolute": {"x": 560, "y": -500}, "dragging": false}], "edges": [{"id": "reactflow__edge-start-nodeflow-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750020115983query", "source": "start-node", "sourceHandle": "flow", "target": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750020115983", "targetHandle": "query", "type": "default", "animated": true, "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750020115983", "sourceHandle": "Generated_String", "target": "AgenticAI-1750043578812", "targetHandle": "query", "type": "default", "id": "reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750020115983Generated_String-AgenticAI-1750043578812query", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "MCP_DesktopCommanderMCP_create_directory-1750043585945", "sourceHandle": "result", "target": "AgenticAI-1750043578812", "targetHandle": "tools", "type": "default", "id": "reactflow__edge-MCP_DesktopCommanderMCP_create_directory-1750043585945result-AgenticAI-1750043578812tools", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "MCP_DesktopCommanderMCP_edit_block-1750043592293", "sourceHandle": "result", "target": "AgenticAI-1750043578812", "targetHandle": "tools", "type": "default", "id": "reactflow__edge-MCP_DesktopCommanderMCP_edit_block-1750043592293result-AgenticAI-1750043578812tools"}]}, "start_node_data": [{"field": "query", "type": "string", "transition_id": "transition-MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750020115983"}, {"field": "path", "type": "string", "transition_id": "transition-MCP_DesktopCommanderMCP_create_directory-1750043585945"}, {"field": "file_path", "type": "string", "transition_id": "transition-MCP_DesktopCommanderMCP_edit_block-1750043592293"}, {"field": "old_string", "type": "string", "transition_id": "transition-MCP_DesktopCommanderMCP_edit_block-1750043592293"}, {"field": "new_string", "type": "string", "transition_id": "transition-MCP_DesktopCommanderMCP_edit_block-1750043592293"}]}