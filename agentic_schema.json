{"name": "Untitled_Workflow", "description": "Untitled_Workflow", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"AgenticAI-1750047131506_query": {"node_id": "AgenticAI-1750047131506", "node_name": "AI Agent Executor", "input_name": "query", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584_query": {"node_id": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584", "node_name": "Tavily Web Search and Extraction Server - tavily-search", "input_name": "query", "connected_to_start": true, "required": true, "input_type": "string", "options": null}, "MCP_Script_Generation_script_generate-1750057787782_topic": {"node_id": "MCP_Script_Generation_script_generate-1750057787782", "node_name": "Script Generation - script_generate", "input_name": "topic", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "AgenticAI-1750047131506", "type": "WorkflowNode", "position": {"x": 420, "y": -20}, "data": {"label": "AI Agent Executor", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "input_type": "credential", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"tools": [{"node_id": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584", "node_type": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search", "node_label": "Tavily Web Search and Extraction Server - tavily-search", "component_id": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584", "component_type": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search", "component_name": "Tavily Web Search and Extraction Server - tavily-search", "component_definition": {"name": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search", "display_name": "Tavily Web Search and Extraction Server - tavily-search", "description": "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "query", "display_name": "query", "info": "Search query", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "search_depth", "display_name": "search depth", "info": "The depth of the search. It can be 'basic' or 'advanced'", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "basic", "options": ["basic", "advanced"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "topic", "display_name": "topic", "info": "The category of the search. This will determine which of our agents will be used for the search", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "general", "options": ["general", "news"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "days", "display_name": "days", "info": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 3, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "time_range", "display_name": "time range", "info": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": ["day", "week", "month", "year", "d", "w", "m", "y"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "max_results", "display_name": "max results", "info": "The maximum number of search results to return", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "include_images", "display_name": "include images", "info": "Include a list of query-related images in the response", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "include_image_descriptions", "display_name": "include image descriptions", "info": "Include a list of query-related images and their descriptions in the response", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "include_raw_content", "display_name": "include raw content", "info": "Include the cleaned and parsed HTML content of each search result", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "include_domains", "display_name": "include domains", "info": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "exclude_domains", "display_name": "exclude domains", "info": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "Generated_String", "display_name": "Generated_String", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_tavily_web_search_and_extraction_server_tavily-search", "type": "MCP", "mcp_info": {"server_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "server_path": "", "tool_name": "tavily-search", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "search_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "The depth of the search. It can be 'basic' or 'advanced'", "default": "basic"}, "topic": {"type": "string", "enum": ["general", "news"], "description": "The category of the search. This will determine which of our agents will be used for the search", "default": "general"}, "days": {"type": "number", "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "default": 3}, "time_range": {"type": "string", "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "enum": ["day", "week", "month", "year", "d", "w", "m", "y"]}, "max_results": {"type": "number", "description": "The maximum number of search results to return", "default": 10, "minimum": 5, "maximum": 20}, "include_images": {"type": "boolean", "description": "Include a list of query-related images in the response", "default": false}, "include_image_descriptions": {"type": "boolean", "description": "Include a list of query-related images and their descriptions in the response", "default": false}, "include_raw_content": {"type": "boolean", "description": "Include the cleaned and parsed HTML content of each search result", "default": false}, "include_domains": {"type": "array", "items": {"type": "string"}, "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "default": []}, "exclude_domains": {"type": "array", "items": {"type": "string"}, "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "default": []}}, "required": ["query"]}, "output_schema": {"properties": {"Generated_String": {"type": "string", "description": "generated string from tavily", "title": "Generated_String"}}}}}, "component_config": {}, "mcp_metadata": {"server_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "server_path": "", "tool_name": "tavily-search", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "search_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "The depth of the search. It can be 'basic' or 'advanced'", "default": "basic"}, "topic": {"type": "string", "enum": ["general", "news"], "description": "The category of the search. This will determine which of our agents will be used for the search", "default": "general"}, "days": {"type": "number", "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "default": 3}, "time_range": {"type": "string", "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "enum": ["day", "week", "month", "year", "d", "w", "m", "y"]}, "max_results": {"type": "number", "description": "The maximum number of search results to return", "default": 10, "minimum": 5, "maximum": 20}, "include_images": {"type": "boolean", "description": "Include a list of query-related images in the response", "default": false}, "include_image_descriptions": {"type": "boolean", "description": "Include a list of query-related images and their descriptions in the response", "default": false}, "include_raw_content": {"type": "boolean", "description": "Include the cleaned and parsed HTML content of each search result", "default": false}, "include_domains": {"type": "array", "items": {"type": "string"}, "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "default": []}, "exclude_domains": {"type": "array", "items": {"type": "string"}, "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "default": []}}, "required": ["query"]}, "output_schema": {"properties": {"Generated_String": {"type": "string", "description": "generated string from tavily", "title": "Generated_String"}}}}}, {"node_id": "MCP_Script_Generation_script_generate-1750057787782", "node_type": "MCP_Script_Generation_script_generate", "node_label": "Script Generation - script_generate", "component_id": "MCP_Script_Generation_script_generate-1750057787782", "component_type": "MCP_Script_Generation_script_generate", "component_name": "Script Generation - script_generate", "component_definition": {"name": "MCP_Script_Generation_script_generate", "display_name": "Script Generation - script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "topic", "display_name": "Topic", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "script_type", "display_name": "script type", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "TOPIC", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "keywords", "display_name": "keywords", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "video_type", "display_name": "video type", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "SHORT", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "title", "display_name": "Title", "output_type": "string"}, {"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "string"}, {"name": "script_type", "display_name": "Script Type", "output_type": "string"}, {"name": "video_type", "display_name": "Video Type", "output_type": "string"}, {"name": "link", "display_name": "Link", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_script_generation_script_generate", "type": "MCP", "mcp_info": {"server_id": "0dc83245-794f-405d-8814-7771260d3c60", "server_path": "", "tool_name": "script_generate", "input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "Title"}, "script": {"type": "string", "description": "The generated script", "title": "<PERSON><PERSON><PERSON>"}, "script_type": {"type": "string", "description": "Type of the script", "title": "Script Type"}, "video_type": {"type": "string", "description": "The type of video", "title": "Video Type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "Link"}}}}}, "component_config": {}, "mcp_metadata": {"server_id": "0dc83245-794f-405d-8814-7771260d3c60", "server_path": "", "tool_name": "script_generate", "input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "Title"}, "script": {"type": "string", "description": "The generated script", "title": "<PERSON><PERSON><PERSON>"}, "script_type": {"type": "string", "description": "Type of the script", "title": "Script Type"}, "video_type": {"type": "string", "description": "The type of video", "title": "Video Type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "Link"}}}}}]}}, "style": {"opacity": 1}, "width": 208, "height": 218, "selected": false, "positionAbsolute": {"x": 420, "y": -20}, "dragging": false}, {"id": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584", "type": "WorkflowNode", "position": {"x": -60, "y": -120}, "data": {"label": "Tavily Web Search and Extraction Server - tavily-search", "type": "mcp", "originalType": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search", "definition": {"name": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search", "display_name": "Tavily Web Search and Extraction Server - tavily-search", "description": "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "query", "display_name": "query", "info": "Search query", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "search_depth", "display_name": "search depth", "info": "The depth of the search. It can be 'basic' or 'advanced'", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "basic", "options": ["basic", "advanced"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "topic", "display_name": "topic", "info": "The category of the search. This will determine which of our agents will be used for the search", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "general", "options": ["general", "news"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "days", "display_name": "days", "info": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 3, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "time_range", "display_name": "time range", "info": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": ["day", "week", "month", "year", "d", "w", "m", "y"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "max_results", "display_name": "max results", "info": "The maximum number of search results to return", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "include_images", "display_name": "include images", "info": "Include a list of query-related images in the response", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "include_image_descriptions", "display_name": "include image descriptions", "info": "Include a list of query-related images and their descriptions in the response", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "include_raw_content", "display_name": "include raw content", "info": "Include the cleaned and parsed HTML content of each search result", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "include_domains", "display_name": "include domains", "info": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "exclude_domains", "display_name": "exclude domains", "info": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "Generated_String", "display_name": "Generated_String", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_tavily_web_search_and_extraction_server_tavily-search", "type": "MCP", "mcp_info": {"server_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "server_path": "", "tool_name": "tavily-search", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "search_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "The depth of the search. It can be 'basic' or 'advanced'", "default": "basic"}, "topic": {"type": "string", "enum": ["general", "news"], "description": "The category of the search. This will determine which of our agents will be used for the search", "default": "general"}, "days": {"type": "number", "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "default": 3}, "time_range": {"type": "string", "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "enum": ["day", "week", "month", "year", "d", "w", "m", "y"]}, "max_results": {"type": "number", "description": "The maximum number of search results to return", "default": 10, "minimum": 5, "maximum": 20}, "include_images": {"type": "boolean", "description": "Include a list of query-related images in the response", "default": false}, "include_image_descriptions": {"type": "boolean", "description": "Include a list of query-related images and their descriptions in the response", "default": false}, "include_raw_content": {"type": "boolean", "description": "Include the cleaned and parsed HTML content of each search result", "default": false}, "include_domains": {"type": "array", "items": {"type": "string"}, "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "default": []}, "exclude_domains": {"type": "array", "items": {"type": "string"}, "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "default": []}}, "required": ["query"]}, "output_schema": {"properties": {"Generated_String": {"type": "string", "description": "generated string from tavily", "title": "Generated_String"}}}}}, "config": {}}, "style": {"opacity": 1}, "width": 210, "height": 426, "selected": false, "positionAbsolute": {"x": -60, "y": -120}, "dragging": false}, {"id": "MCP_Script_Generation_script_generate-1750057787782", "type": "WorkflowNode", "position": {"x": -120, "y": -400}, "data": {"label": "Script Generation - script_generate", "type": "mcp", "originalType": "MCP_Script_Generation_script_generate", "definition": {"name": "MCP_Script_Generation_script_generate", "display_name": "Script Generation - script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "category": "MCP", "icon": "Cloud", "beta": true, "inputs": [{"name": "topic", "display_name": "Topic", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "script_type", "display_name": "script type", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "TOPIC", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "keywords", "display_name": "keywords", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "video_type", "display_name": "video type", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "SHORT", "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "title", "display_name": "Title", "output_type": "string"}, {"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "string"}, {"name": "script_type", "display_name": "Script Type", "output_type": "string"}, {"name": "video_type", "display_name": "Video Type", "output_type": "string"}, {"name": "link", "display_name": "Link", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_script_generation_script_generate", "type": "MCP", "mcp_info": {"server_id": "0dc83245-794f-405d-8814-7771260d3c60", "server_path": "", "tool_name": "script_generate", "input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "Title"}, "script": {"type": "string", "description": "The generated script", "title": "<PERSON><PERSON><PERSON>"}, "script_type": {"type": "string", "description": "Type of the script", "title": "Script Type"}, "video_type": {"type": "string", "description": "The type of video", "title": "Video Type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "Link"}}}}}, "config": {}}, "style": {"opacity": 1}, "width": 210, "height": 234, "selected": false, "positionAbsolute": {"x": -120, "y": -400}, "dragging": false}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "start-node", "sourceHandle": "flow", "target": "AgenticAI-1750047131506", "targetHandle": "query", "type": "default", "id": "reactflow__edge-start-nodeflow-AgenticAI-1750047131506query", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584", "sourceHandle": "Generated_String", "target": "AgenticAI-1750047131506", "targetHandle": "tools", "type": "default", "id": "reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584Generated_String-AgenticAI-1750047131506tools", "selected": false}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "MCP_Script_Generation_script_generate-1750057787782", "sourceHandle": "title", "target": "AgenticAI-1750047131506", "targetHandle": "tools", "type": "default", "id": "reactflow__edge-MCP_Script_Generation_script_generate-1750057787782title-AgenticAI-1750047131506tools"}]}, "start_node_data": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1750047131506"}, {"field": "query", "type": "string", "transition_id": "transition-MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584"}, {"field": "topic", "type": "string", "transition_id": "transition-MC<PERSON>_Script_Generation_script_generate-1750057787782"}]}