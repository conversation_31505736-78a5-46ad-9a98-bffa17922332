{"nodes": [{"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "api_key", "data_type": {"type": "string", "description": "API key for the model provider. Can be entered directly or referenced from secure storage."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "description", "data_type": {"type": "string", "description": "Description of the agent for UI display."}, "required": false}, {"field_name": "execution_type", "data_type": {"type": "string", "description": "Determines if agent handles single response or multi-turn conversation."}, "required": false}, {"field_name": "query", "data_type": {"type": "string", "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "system_message", "data_type": {"type": "string", "description": "System prompt/instructions for the agent. If empty, will use default based on query."}, "required": false}, {"field_name": "termination_condition", "data_type": {"type": "string", "description": "Defines when multi-turn conversations should end. Required for interactive execution type."}, "required": false}, {"field_name": "max_tokens", "data_type": {"type": "number", "description": "Maximum response length in tokens."}, "required": false}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "autogen_agent_type", "data_type": {"type": "string", "description": "The type of AutoGen agent to create internally."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": "", "format": "datetime"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}], "transitions": [{"id": "transition-AgenticAI-1750062782493", "sequence": 1, "transition_type": "initial", "execution_type": "agent", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "agent_type", "data_type": "string", "field_value": "component"}, {"field_name": "execution_type", "data_type": "string", "field_value": "response"}, {"field_name": "query", "data_type": "string", "field_value": ""}, {"field_name": "agent_config", "data_type": "object", "field_value": {"agent_tools": [{"tool_type": "workflow_component", "component": {"component_id": "MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750062802423", "component_type": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search", "component_name": "Tavily Web Search and Extraction Server - tavily-search", "component_description": "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", "input_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "search_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "The depth of the search. It can be 'basic' or 'advanced'", "default": "basic"}, "topic": {"type": "string", "enum": ["general", "news"], "description": "The category of the search. This will determine which of our agents will be used for the search", "default": "general"}, "days": {"type": "number", "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "default": 3}, "time_range": {"type": "string", "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "enum": ["day", "week", "month", "year", "d", "w", "m", "y"]}, "max_results": {"type": "number", "description": "The maximum number of search results to return", "default": 10, "minimum": 5, "maximum": 20}, "include_images": {"type": "boolean", "description": "Include a list of query-related images in the response", "default": false}, "include_image_descriptions": {"type": "boolean", "description": "Include a list of query-related images and their descriptions in the response", "default": false}, "include_raw_content": {"type": "boolean", "description": "Include the cleaned and parsed HTML content of each search result", "default": false}, "include_domains": {"type": "array", "items": {"type": "string"}, "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "default": []}, "exclude_domains": {"type": "array", "items": {"type": "string"}, "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "default": []}}, "required": ["query"]}, "output_schema": {"properties": {"Generated_String": {"type": "string", "description": "generated string from tavily", "title": "Generated_String"}}}, "mcp_metadata": {"server_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "server_path": "", "tool_name": "tavily-search", "tool_schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "search_depth": {"type": "string", "enum": ["basic", "advanced"], "description": "The depth of the search. It can be 'basic' or 'advanced'", "default": "basic"}, "topic": {"type": "string", "enum": ["general", "news"], "description": "The category of the search. This will determine which of our agents will be used for the search", "default": "general"}, "days": {"type": "number", "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", "default": 3}, "time_range": {"type": "string", "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", "enum": ["day", "week", "month", "year", "d", "w", "m", "y"]}, "max_results": {"type": "number", "description": "The maximum number of search results to return", "default": 10, "minimum": 5, "maximum": 20}, "include_images": {"type": "boolean", "description": "Include a list of query-related images in the response", "default": false}, "include_image_descriptions": {"type": "boolean", "description": "Include a list of query-related images and their descriptions in the response", "default": false}, "include_raw_content": {"type": "boolean", "description": "Include the cleaned and parsed HTML content of each search result", "default": false}, "include_domains": {"type": "array", "items": {"type": "string"}, "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "default": []}, "exclude_domains": {"type": "array", "items": {"type": "string"}, "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "default": []}}, "required": ["query"]}}}}, {"tool_type": "workflow_component", "component": {"component_id": "MCP_Script_Generation_script_generate-1750062817323", "component_type": "MCP_Script_Generation_script_generate", "component_name": "Script Generation - script_generate", "component_description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "input_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}, "output_schema": {"properties": {"title": {"type": "string", "description": "Title of the generated script", "title": "Title"}, "script": {"type": "string", "description": "The generated script", "title": "<PERSON><PERSON><PERSON>"}, "script_type": {"type": "string", "description": "Type of the script", "title": "Script Type"}, "video_type": {"type": "string", "description": "The type of video", "title": "Video Type"}, "link": {"type": "string", "format": "uri", "description": "Optional link for the script", "title": "Link"}}}, "mcp_metadata": {"server_id": "0dc83245-794f-405d-8814-7771260d3c60", "server_path": "", "tool_name": "script_generate", "tool_schema": {"$defs": {"Keywords": {"properties": {"time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Time"}, "objective": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Objective"}, "audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Audience"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Gender"}, "tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "speakers": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Speakers"}}, "title": "Keywords", "type": "object"}, "ScriptType": {"enum": ["VIDEO", "TOPIC", "SCRIPT", "BLOG", "AI"], "title": "ScriptType", "type": "string"}, "VideoType": {"enum": ["SHORT", "LONG"], "title": "VideoType", "type": "string"}}, "properties": {"topic": {"title": "Topic", "type": "string"}, "script_type": {"$ref": "#/$defs/ScriptType", "default": "TOPIC"}, "keywords": {"$ref": "#/$defs/Keywords"}, "video_type": {"$ref": "#/$defs/VideoType", "default": "SHORT"}, "link": {"anyOf": [{"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, {"type": "null"}], "default": null, "title": "Link"}}, "required": ["topic"], "title": "GenerateScriptInput", "type": "object"}}}}]}}]}}], "input_data": [{"from_transition_id": "transition-MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750062802423", "source_node_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750062802423", "source_handle_id": "Generated_String", "target_handle_id": "tools", "edge_id": "reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750062802423Generated_String-AgenticAI-1750062782493tools"}]}, {"from_transition_id": "transition-MCP_Script_Generation_script_generate-1750062817323", "source_node_id": "0dc83245-794f-405d-8814-7771260d3c60", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-MCP_Script_Generation_script_generate-1750062817323", "source_handle_id": "title", "target_handle_id": "tools", "edge_id": "reactflow__edge-MCP_Script_Generation_script_generate-1750062817323title-AgenticAI-1750062782493tools"}]}], "output_data": []}, "result_resolution": {"node_type": "agent", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "query", "handle_name": "Query/Objective", "data_type": "string", "required": true, "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, {"handle_id": "input_variables", "handle_name": "Input Variables", "data_type": "object", "required": false, "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, {"handle_id": "tools", "handle_name": "Tools", "data_type": "string", "required": false, "description": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle."}, {"handle_id": "memory", "handle_name": "Memory Object", "data_type": "string", "required": false, "description": "Connect a memory object from another node."}], "output_handles": [{"handle_id": "final_answer", "handle_name": "Final Answer", "data_type": "string", "description": ""}, {"handle_id": "intermediate_steps", "handle_name": "Intermediate Steps", "data_type": "string", "description": ""}, {"handle_id": "updated_memory", "handle_name": "Updated Memory", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"final_answer": "final_answer", "intermediate_steps": "intermediate_steps", "updated_memory": "updated_memory", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.final_answer", "output_data.final_answer", "response.final_answer", "data.final_answer", "result.intermediate_steps", "output_data.intermediate_steps", "response.intermediate_steps", "data.intermediate_steps", "result.updated_memory", "output_data.updated_memory", "response.updated_memory", "data.updated_memory", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "final_answer"}}, "approval_required": false, "end": true}]}