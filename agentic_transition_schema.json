{"nodes": [{"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "api_key", "data_type": {"type": "string", "description": "API key for the model provider. Can be entered directly or referenced from secure storage."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "description", "data_type": {"type": "string", "description": "Description of the agent for UI display."}, "required": false}, {"field_name": "execution_type", "data_type": {"type": "string", "description": "Determines if agent handles single response or multi-turn conversation."}, "required": false}, {"field_name": "query", "data_type": {"type": "string", "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "system_message", "data_type": {"type": "string", "description": "System prompt/instructions for the agent. If empty, will use default based on query."}, "required": false}, {"field_name": "termination_condition", "data_type": {"type": "string", "description": "Defines when multi-turn conversations should end. Required for interactive execution type."}, "required": false}, {"field_name": "max_tokens", "data_type": {"type": "number", "description": "Maximum response length in tokens."}, "required": false}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "tools", "data_type": {"type": "string", "description": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "autogen_agent_type", "data_type": {"type": "string", "description": "The type of AutoGen agent to create internally."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": "", "format": "datetime"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "tavily-search", "input_schema": {"predefined_fields": [{"field_name": "query", "data_type": {"type": "string", "description": "Search query"}, "required": true}, {"field_name": "search_depth", "data_type": {"type": "string", "description": "The depth of the search. It can be 'basic' or 'advanced'"}, "required": false}, {"field_name": "topic", "data_type": {"type": "string", "description": "The category of the search. This will determine which of our agents will be used for the search"}, "required": false}, {"field_name": "days", "data_type": {"type": "number", "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic"}, "required": false}, {"field_name": "time_range", "data_type": {"type": "string", "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics"}, "required": false}, {"field_name": "max_results", "data_type": {"type": "number", "description": "The maximum number of search results to return"}, "required": false}, {"field_name": "include_images", "data_type": {"type": "boolean", "description": "Include a list of query-related images in the response"}, "required": false}, {"field_name": "include_image_descriptions", "data_type": {"type": "boolean", "description": "Include a list of query-related images and their descriptions in the response"}, "required": false}, {"field_name": "include_raw_content", "data_type": {"type": "boolean", "description": "Include the cleaned and parsed HTML content of each search result"}, "required": false}, {"field_name": "include_domains", "data_type": {"type": "array", "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site", "items": {"type": "string"}}, "required": false}, {"field_name": "exclude_domains", "data_type": {"type": "array", "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site", "items": {"type": "string"}}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "Generated_String", "data_type": {"type": "string", "description": "generated string from tavily", "format": "percentage"}}]}}]}, {"id": "0dc83245-794f-405d-8814-7771260d3c60", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "script_generate", "input_schema": {"predefined_fields": [{"field_name": "topic", "data_type": {"type": "string", "description": ""}, "required": true}, {"field_name": "script_type", "data_type": {"type": "string", "description": ""}, "required": false}, {"field_name": "keywords", "data_type": {"type": "string", "description": ""}, "required": false}, {"field_name": "video_type", "data_type": {"type": "string", "description": ""}, "required": false}, {"field_name": "link", "data_type": {"type": "string", "description": ""}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "title", "data_type": {"type": "string", "description": "Title of the generated script", "format": "string"}}, {"field_name": "script", "data_type": {"type": "string", "description": "The generated script", "format": "string"}}, {"field_name": "script_type", "data_type": {"type": "string", "description": "Type of the script", "format": "string"}}, {"field_name": "video_type", "data_type": {"type": "string", "description": "The type of video", "format": "video"}}, {"field_name": "link", "data_type": {"type": "string", "description": "Optional link for the script", "format": "uri"}}]}}]}], "transitions": [{"id": "transition-MC<PERSON>_Script_Generation_script_generate-1750057787782", "sequence": 1, "transition_type": "initial", "execution_type": "MCP", "node_info": {"node_id": "0dc83245-794f-405d-8814-7771260d3c60", "tools_to_use": [{"tool_id": 1, "tool_name": "script_generate", "tool_params": {"items": [{"field_name": "topic", "data_type": "string", "field_value": null}, {"field_name": "script_type", "data_type": "string", "field_value": null}, {"field_name": "keywords", "data_type": "string", "field_value": null}, {"field_name": "video_type", "data_type": "string", "field_value": null}, {"field_name": "link", "data_type": "string", "field_value": null}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-AgenticAI-1750047131506", "target_node_id": "AI Agent Executor", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "title", "result_path": "title", "edge_id": "reactflow__edge-MCP_Script_Generation_script_generate-1750057787782title-AgenticAI-1750047131506tools"}]}}]}, "result_resolution": {"node_type": "mcp", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "topic", "handle_name": "Topic", "data_type": "string", "required": true, "description": ""}, {"handle_id": "script_type", "handle_name": "script type", "data_type": "string", "required": false, "description": ""}, {"handle_id": "keywords", "handle_name": "keywords", "data_type": "string", "required": false, "description": ""}, {"handle_id": "video_type", "handle_name": "video type", "data_type": "string", "required": false, "description": ""}, {"handle_id": "link", "handle_name": "Link", "data_type": "string", "required": false, "description": ""}], "output_handles": [{"handle_id": "title", "handle_name": "Title", "data_type": "string", "description": ""}, {"handle_id": "script", "handle_name": "<PERSON><PERSON><PERSON>", "data_type": "string", "description": ""}, {"handle_id": "script_type", "handle_name": "Script Type", "data_type": "string", "description": ""}, {"handle_id": "video_type", "handle_name": "Video Type", "data_type": "string", "description": ""}, {"handle_id": "link", "handle_name": "Link", "data_type": "string", "description": ""}]}, "result_path_hints": {"title": "title", "script": "script", "script_type": "script_type", "video_type": "video_type", "link": "link"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.title", "output_data.title", "response.title", "data.title", "result.script", "output_data.script", "response.script", "data.script", "result.script_type", "output_data.script_type", "response.script_type", "data.script_type", "result.video_type", "output_data.video_type", "response.video_type", "data.video_type", "result.link", "output_data.link", "response.link", "data.link", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "title"}}, "approval_required": false, "end": false}, {"id": "transition-AgenticAI-1750047131506", "sequence": 2, "transition_type": "initial", "execution_type": "agent", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "agent_type", "data_type": "string", "field_value": "component"}, {"field_name": "execution_type", "data_type": "string", "field_value": "response"}, {"field_name": "query", "data_type": "string", "field_value": ""}, {"field_name": "agent_config", "data_type": "object", "field_value": {}}]}}], "input_data": [{"from_transition_id": "transition-MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584", "source_node_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584", "source_handle_id": "Generated_String", "target_handle_id": "tools", "edge_id": "reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584Generated_String-AgenticAI-1750047131506tools"}]}, {"from_transition_id": "transition-MC<PERSON>_Script_Generation_script_generate-1750057787782", "source_node_id": "0dc83245-794f-405d-8814-7771260d3c60", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-MC<PERSON>_Script_Generation_script_generate-1750057787782", "source_handle_id": "title", "target_handle_id": "tools", "edge_id": "reactflow__edge-MCP_Script_Generation_script_generate-1750057787782title-AgenticAI-1750047131506tools"}]}], "output_data": []}, "result_resolution": {"node_type": "agent", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "query", "handle_name": "Query/Objective", "data_type": "string", "required": true, "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, {"handle_id": "input_variables", "handle_name": "Input Variables", "data_type": "object", "required": false, "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, {"handle_id": "tools", "handle_name": "Tools", "data_type": "string", "required": false, "description": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle."}, {"handle_id": "memory", "handle_name": "Memory Object", "data_type": "string", "required": false, "description": "Connect a memory object from another node."}], "output_handles": [{"handle_id": "final_answer", "handle_name": "Final Answer", "data_type": "string", "description": ""}, {"handle_id": "intermediate_steps", "handle_name": "Intermediate Steps", "data_type": "string", "description": ""}, {"handle_id": "updated_memory", "handle_name": "Updated Memory", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"final_answer": "final_answer", "intermediate_steps": "intermediate_steps", "updated_memory": "updated_memory", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.final_answer", "output_data.final_answer", "response.final_answer", "data.final_answer", "result.intermediate_steps", "output_data.intermediate_steps", "response.intermediate_steps", "data.intermediate_steps", "result.updated_memory", "output_data.updated_memory", "response.updated_memory", "data.updated_memory", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "final_answer"}}, "approval_required": false, "end": true}, {"id": "transition-MCP_<PERSON><PERSON>_Web_Search_and_Extraction_Server_tavily-search-1750057756584", "sequence": 3, "transition_type": "standard", "execution_type": "MCP", "node_info": {"node_id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "tools_to_use": [{"tool_id": 1, "tool_name": "tavily-search", "tool_params": {"items": [{"field_name": "query", "data_type": "string", "field_value": null}, {"field_name": "search_depth", "data_type": "string", "field_value": null}, {"field_name": "topic", "data_type": "string", "field_value": null}, {"field_name": "days", "data_type": "string", "field_value": null}, {"field_name": "time_range", "data_type": "string", "field_value": null}, {"field_name": "max_results", "data_type": "string", "field_value": null}, {"field_name": "include_images", "data_type": "boolean", "field_value": null}, {"field_name": "include_image_descriptions", "data_type": "boolean", "field_value": null}, {"field_name": "include_raw_content", "data_type": "boolean", "field_value": null}, {"field_name": "include_domains", "data_type": "string", "field_value": null}, {"field_name": "exclude_domains", "data_type": "string", "field_value": null}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-AgenticAI-1750047131506", "target_node_id": "AI Agent Executor", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "Generated_String", "result_path": "Generated_String", "edge_id": "reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584Generated_String-AgenticAI-1750047131506tools"}]}}]}, "result_resolution": {"node_type": "mcp", "expected_result_structure": "dynamic", "handle_registry": {"input_handles": [{"handle_id": "query", "handle_name": "query", "data_type": "string", "required": true, "description": "Search query"}, {"handle_id": "search_depth", "handle_name": "search depth", "data_type": "string", "required": false, "description": "The depth of the search. It can be 'basic' or 'advanced'"}, {"handle_id": "topic", "handle_name": "topic", "data_type": "string", "required": false, "description": "The category of the search. This will determine which of our agents will be used for the search"}, {"handle_id": "days", "handle_name": "days", "data_type": "string", "required": false, "description": "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic"}, {"handle_id": "time_range", "handle_name": "time range", "data_type": "string", "required": false, "description": "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics"}, {"handle_id": "max_results", "handle_name": "max results", "data_type": "string", "required": false, "description": "The maximum number of search results to return"}, {"handle_id": "include_images", "handle_name": "include images", "data_type": "boolean", "required": false, "description": "Include a list of query-related images in the response"}, {"handle_id": "include_image_descriptions", "handle_name": "include image descriptions", "data_type": "boolean", "required": false, "description": "Include a list of query-related images and their descriptions in the response"}, {"handle_id": "include_raw_content", "handle_name": "include raw content", "data_type": "boolean", "required": false, "description": "Include the cleaned and parsed HTML content of each search result"}, {"handle_id": "include_domains", "handle_name": "include domains", "data_type": "string", "required": false, "description": "A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site"}, {"handle_id": "exclude_domains", "handle_name": "exclude domains", "data_type": "string", "required": false, "description": "List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site"}], "output_handles": [{"handle_id": "Generated_String", "handle_name": "Generated_String", "data_type": "string", "description": ""}]}, "result_path_hints": {"Generated_String": "Generated_String"}, "dynamic_discovery": {"enabled": true, "fallback_patterns": ["result.Generated_String", "output_data.Generated_String", "response.Generated_String", "data.Generated_String", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": false, "supports_nested_results": true, "requires_dynamic_discovery": true, "primary_output_handle": "Generated_String"}}, "approval_required": false, "end": false}]}