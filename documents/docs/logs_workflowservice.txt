case-insensitive substring matching.\n                        \n                        Use this instead of \'execute_command\' with find/dir/ls for locating files.\n                        Searches through all subdirectories from the starting path.\n                        \n                        Has a default timeout of 30 seconds which can be customized using the timeoutMs parameter.\n                        Only searches within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with \'/\' or drive letter like \'C:\\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions.', 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'path', 'display_name': 'path', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'pattern', 'display_name': 'pattern', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'timeoutMs', 'display_name': 'timeoutMs', 'info': '', 'input_type': 'number', 'input_types': ['number', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}], 'outputs': [{'name': 'result', 'display_name': 'Result', 'output_type': 'any'}], 'is_valid': True, 'path': 'mcp.mcp_desktopcommandermcp_search_files', 'type': 'MCP', 'mcp_info': {'server_id': 'd94e4d15-7459-4969-91a9-bbe3aeef08e6', 'server_path': '', 'tool_name': 'search_files', 'input_schema': {'type': 'object', 'properties': {'path': {'type': 'string'}, 'pattern': {'type': 'string'}, 'timeoutMs': {'type': 'number'}}, 'required': ['path', 'pattern'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, 'output_schema': None}}, 'component_config': {}, 'mcp_metadata': {'server_id': 'd94e4d15-7459-4969-91a9-bbe3aeef08e6', 'server_path': '', 'tool_name': 'search_files', 'input_schema': {'type': 'object', 'properties': {'path': {'type': 'string'}, 'pattern': {'type': 'string'}, 'timeoutMs': {'type': 'number'}}, 'required': ['path', 'pattern'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, 'output_schema': None}}]}}, 'style': {'opacity': 1}, 'width': 208, 'height': 218, 'selected': False, 'positionAbsolute': {'x': 420, 'y': -20}, 'dragging': False}, {'id': 'MCP_Candidate_Interview_candidate_suitability-1750047143304', 'type': 'WorkflowNode', 'position': {'x': -120, 'y': -140}, 'data': {'label': 'Candidate Interview - candidate_suitability', 'type': 'mcp', 'originalType': 'MCP_Candidate_Interview_candidate_suitability', 'definition': {'name': 'MCP_Candidate_Interview_candidate_suitability', 'display_name': 'Candidate Interview - candidate_suitability', 'description': 'Analyze candidate suitability based on job description and resume', 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'resume_details', 'display_name': 'Resume Details', 'info': " candidate's resume", 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'jd_details', 'display_name': 'Jd Details', 'info': 'job description', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'result', 'display_name': 'Result', 'output_type': 'any'}], 'is_valid': True, 'path': 'mcp.mcp_candidate_interview_candidate_suitability', 'type': 'MCP', 'mcp_info': {'server_id': '24e34760-ac08-4117-be8c-372b7a6b9f31', 'server_path': '', 'tool_name': 'candidate_suitability', 'input_schema': {'properties': {'resume_details': {'description': " candidate's resume", 'title': 'Resume Details', 'type': 'string'}, 'jd_details': {'description': 'job description', 'title': 'Jd Details', 'type': 'string'}}, 'required': ['resume_details', 'jd_details'], 'title': 'CandidateSuitabilitySchema', 'type': 'object'}, 'output_schema': None}}, 'config': {}}, 'style': {'opacity': 1}, 'width': 210, 'height': 150, 'selected': False}, {'id': 'MCP_DesktopCommanderMCP_search_files-1750047151487', 'type': 'WorkflowNode', 'position': {'x': 0, 'y': -480}, 'data': {'label': 'DesktopCommanderMCP - search_files', 'type': 'mcp', 'originalType': 'MCP_DesktopCommanderMCP_search_files', 'definition': {'name': 'MCP_DesktopCommanderMCP_search_files', 'display_name': 'DesktopCommanderMCP - search_files', 'description': '\n                        Finds files by name using a case-insensitive substring matching.\n                        \n                        Use this instead of \'execute_command\' with find/dir/ls for locating files.\n                        Searches through all subdirectories from the starting path.\n                        \n                        Has a default timeout of 30 seconds which can be customized using the timeoutMs parameter.\n                        Only searches within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with \'/\' or drive letter like \'C:\\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as "DC: ..." or "use Desktop Commander to ..." in your instructions.', 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'path', 'display_name': 'path', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'pattern', 'display_name': 'pattern', 'info': '', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}, {'name': 'timeoutMs', 'display_name': 'timeoutMs', 'info': '', 'input_type': 'number', 'input_types': ['number', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'validation': {}}], 'outputs': [{'name': 'result', 'display_name': 'Result', 'output_type': 'any'}], 'is_valid': True, 'path': 'mcp.mcp_desktopcommandermcp_search_files', 'type': 'MCP', 'mcp_info': {'server_id': 'd94e4d15-7459-4969-91a9-bbe3aeef08e6', 'server_path': '', 'tool_name': 'search_files', 'input_schema': {'type': 'object', 'properties': {'path': {'type': 'string'}, 'pattern': {'type': 'string'}, 'timeoutMs': {'type': 'number'}}, 'required': ['path', 'pattern'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, 'output_schema': None}}, 'config': {}}, 'style': {'opacity': 1}, 'width': 208, 'height': 178, 'selected': True, 'positionAbsolute': {'x': 0, 'y': -480}, 'dragging': False}], 'edges': [{'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'AgenticAI-1750047131506', 'targetHandle': 'query', 'type': 'default', 'id': 'reactflow__edge-start-nodeflow-AgenticAI-1750047131506query', 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'MCP_Candidate_Interview_candidate_suitability-1750047143304', 'sourceHandle': 'result', 'target': 'AgenticAI-1750047131506', 'targetHandle': 'tools', 'type': 'default', 'id': 'reactflow__edge-MCP_Candidate_Interview_candidate_suitability-1750047143304result-AgenticAI-1750047131506tools', 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'MCP_DesktopCommanderMCP_search_files-1750047151487', 'sourceHandle': 'result', 'target': 'AgenticAI-1750047131506', 'targetHandle': 'tools', 'type': 'default', 'id': 'reactflow__edge-MCP_DesktopCommanderMCP_search_files-1750047151487result-AgenticAI-1750047131506tools', 'selected': False}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/a63f1fd1-ee98-4d76-9146-2b261fcc5fd1.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/a63f1fd1-ee98-4d76-9146-2b261fcc5fd1.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges']
[DEBUG] Number of nodes: 4
[DEBUG] Number of edges: 3
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=AgenticAI-1750047131506, type=agent, originalType=AgenticAI
[DEBUG] Node 2: id=MCP_Candidate_Interview_candidate_suitability-1750047143304, type=mcp, originalType=MCP_Candidate_Interview_candidate_suitability
[DEBUG] Node 3: id=MCP_DesktopCommanderMCP_search_files-1750047151487, type=mcp, originalType=MCP_DesktopCommanderMCP_search_files
[DEBUG] Edge 0: id=reactflow__edge-start-nodeflow-AgenticAI-1750047131506query, source=start-node, target=AgenticAI-1750047131506, sourceHandle=flow
[DEBUG] Edge 1: id=reactflow__edge-MCP_Candidate_Interview_candidate_suitability-1750047143304result-AgenticAI-1750047131506tools, source=MCP_Candidate_Interview_candidate_suitability-1750047143304, target=AgenticAI-1750047131506, sourceHandle=result
[DEBUG] Edge 2: id=reactflow__edge-MCP_DesktopCommanderMCP_search_files-1750047151487result-AgenticAI-1750047131506tools, source=MCP_DesktopCommanderMCP_search_files-1750047151487, target=AgenticAI-1750047131506, sourceHandle=result

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 4
   - Edges: 3
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ✅ Found tool node MCP_Candidate_Interview_candidate_suitability-1750047143304 as separate visual node
   ✅ Tool nodes are already saved as separate visual components
   ✅ Skipping virtual node creation to avoid duplicates
   ✅ Using existing tool nodes and edges
   🔧 CONFIGURING TOOL DATA FLOW FOR EXISTING NODES...
         - Configured 2 tools for AgenticAI node AgenticAI-1750047131506
   ✅ Tool data flow configured for existing tool nodes
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(AgenticAI-1750047131506): node_type='agent', original_type='AgenticAI', result=False
[DEBUG] is_conditional_node(MCP_Candidate_Interview_candidate_suitability-1750047143304): node_type='mcp', original_type='MCP_Candidate_Interview_candidate_suitability', result=False
[DEBUG] is_conditional_node(MCP_DesktopCommanderMCP_search_files-1750047151487): node_type='mcp', original_type='MCP_DesktopCommanderMCP_search_files', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - AgenticAI: 1
   - MCP_Candidate_Interview_candidate_suitability: 1
   - MCP_DesktopCommanderMCP_search_files: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
   ℹ️  Allowing multiple connections to AgenticAI-1750047131506.tools (tools handle accepts multiple connections)
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - AgenticAI-1750047131506
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 3
   - Edge mappings: 3
   - All nodes: 4
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 3 nodes
   - Grouped into 1 levels

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/4: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/4: AgenticAI-1750047131506
   Type: AgenticAI (agent)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(AgenticAI-1750047131506): node_type='agent', original_type='AgenticAI', result=False
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 3/4: MCP_Candidate_Interview_candidate_suitability-1750047143304
   Type: MCP_Candidate_Interview_candidate_suitability (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Candidate_Interview_candidate_suitability-1750047143304): node_type='mcp', original_type='MCP_Candidate_Interview_candidate_suitability', result=False
   ❌ ERROR converting node MCP_Candidate_Interview_candidate_suitability-1750047143304: argument of type 'NoneType' is not iterable
[DEBUG] Workflow conversion failed for PATCH: argument of type 'NoneType' is not iterable
[DEBUG] Exception type: TypeError
[DEBUG] Full traceback for NoneType error:
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/services/workflow_functions.py", line 694, in updateWorkflow
    converted_workflow = convert_workflow_to_transition_schema(parsed_workflow_data)
  File "/Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/services/workflow_builder/workflow_schema_converter.py", line 2631, in convert_workflow_to_transition_schema
    transition_node = convert_node_to_transition_node(node, mcp_configs)
  File "/Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/services/workflow_builder/workflow_schema_converter.py", line 1153, in convert_node_to_transition_node
    if "properties" in mcp_output_schema:
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: argument of type 'NoneType' is not iterable
2025-06-16 12:39:56 - workflow-service - INFO - updateWorkflow request received
2025-06-16 12:39:56 [info     ] update_workflow_request        update_mask=['name', 'description', 'workflow_data', 'start_nodes'] workflow_id=4e06cd30-4f3e-486e-9443-91d195a4288a
[DEBUG] 'workflow_data' is in update_mask. Processing...
[DEBUG] Successfully parsed workflow_data JSON for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'start-node', 'type': 'WorkflowNode', 'position': {'x': 100, 'y': 100}, 'data': {'label': 'Start', 'type': 'component', 'originalType': 'StartNode', 'definition': {'name': 'StartNode', 'display_name': 'Start', 'description': 'The starting point for all workflows. Only nodes connected to this node will be executed.', 'category': 'Input/Output', 'icon': 'Play', 'beta': False, 'inputs': [], 'outputs': [{'name': 'flow', 'display_name': 'Flow', 'output_type': 'Any'}], 'is_valid': True, 'path': 'components.io.start_node'}, 'config': {'collected_parameters': {'AgenticAI-1750047131506_query': {'node_id': 'AgenticAI-1750047131506', 'node_name': 'AI Agent Executor', 'input_name': 'query', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}, 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584_query': {'node_id': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'node_name': 'Tavily Web Search and Extraction Server - tavily-search', 'input_name': 'query', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}, 'MCP_Script_Generation_script_generate-1750057787782_topic': {'node_id': 'MCP_Script_Generation_script_generate-1750057787782', 'node_name': 'Script Generation - script_generate', 'input_name': 'topic', 'connected_to_start': True, 'required': True, 'input_type': 'string', 'options': None}}}}, 'width': 208, 'height': 122, 'selected': False, 'dragging': False}, {'id': 'AgenticAI-1750047131506', 'type': 'WorkflowNode', 'position': {'x': 420, 'y': -20}, 'data': {'label': 'AI Agent Executor', 'type': 'agent', 'originalType': 'AgenticAI', 'definition': {'name': 'AgenticAI', 'display_name': 'AI Agent Executor', 'description': 'Executes an AI agent with tools and memory using AutoGen.', 'category': 'AI', 'icon': 'Bot', 'beta': True, 'requires_approval': False, 'inputs': [{'name': 'model_provider', 'display_name': 'Model Provider', 'info': 'The AI model provider to use.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'OpenAI', 'options': ['OpenAI', 'Azure OpenAI', 'Anthropic', 'Claude', 'Google', 'Gemini', 'Mistral', 'Ollama', 'Custom'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'base_url', 'display_name': 'Base URL', 'info': 'Base URL for the API (leave empty for default provider URL).', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'model_provider', 'field_value': 'Custom', 'operator': 'equals'}, {'field_name': 'model_provider', 'field_value': 'Azure OpenAI', 'operator': 'equals'}, {'field_name': 'model_provider', 'field_value': 'Ollama', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'api_key', 'display_name': 'API Key', 'info': 'API key for the model provider. Can be entered directly or referenced from secure storage.', 'input_type': 'credential', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR', 'credential_type': 'api_key', 'use_credential_id': False, 'credential_id': ''}, {'name': 'model_name', 'display_name': 'Model', 'info': 'Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'gpt-4o', 'options': ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo', 'claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-2.1', 'claude-2.0', 'gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro', 'gemini-pro-vision', 'mistral-large-latest', 'mistral-medium-latest', 'mistral-small-latest', 'open-mistral-7b', 'open-mixtral-8x7b', 'open-mixtral-8x22b', 'llama3.2', 'llama3.1', 'llama3', 'llama2', 'mistral', 'mixtral', 'phi3', 'gemma', 'codellama', 'qwen2'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'temperature', 'display_name': 'Temperature', 'info': 'Controls randomness: 0 is deterministic, higher values are more random.', 'input_type': 'float', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 0.7, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'description', 'display_name': 'Description', 'info': 'Description of the agent for UI display.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'execution_type', 'display_name': 'Execution Type', 'info': 'Determines if agent handles single response or multi-turn conversation.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'response', 'options': ['response', 'interactive'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'query', 'display_name': 'Query/Objective', 'info': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'system_message', 'display_name': 'System Message', 'info': 'System prompt/instructions for the agent. If empty, will use default based on query.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'termination_condition', 'display_name': 'Termination Condition', 'info': 'Defines when multi-turn conversations should end. Required for interactive execution type.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'execution_type', 'field_value': 'interactive', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'max_tokens', 'display_name': 'Max Tokens', 'info': 'Maximum response length in tokens.', 'input_type': 'int', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 1000, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_variables', 'display_name': 'Input Variables', 'info': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'tools', 'display_name': 'Tools', 'info': 'Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.', 'input_type': 'handle', 'input_types': ['Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'memory', 'display_name': 'Memory Object', 'info': 'Connect a memory object from another node.', 'input_type': 'handle', 'input_types': ['Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'autogen_agent_type', 'display_name': 'AutoGen Agent Type', 'info': 'The type of AutoGen agent to create internally.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': True, 'value': 'Assistant', 'options': ['Assistant', 'UserProxy', 'CodeExecutor'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}], 'outputs': [{'name': 'final_answer', 'display_name': 'Final Answer', 'output_type': 'string', 'semantic_type': None, 'method': None}, {'name': 'intermediate_steps', 'display_name': 'Intermediate Steps', 'output_type': 'list', 'semantic_type': None, 'method': None}, {'name': 'updated_memory', 'display_name': 'Updated Memory', 'output_type': 'Memory', 'semantic_type': None, 'method': None}, {'name': 'error', 'display_name': 'Error', 'output_type': 'str', 'semantic_type': None, 'method': None}], 'is_valid': True, 'path': 'components.ai.agenticai', 'interface_issues': []}, 'config': {'tools': [{'node_id': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'node_type': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'node_label': 'Tavily Web Search and Extraction Server - tavily-search', 'component_id': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'component_type': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'component_name': 'Tavily Web Search and Extraction Server - tavily-search', 'component_definition': {'name': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'display_name': 'Tavily Web Search and Extraction Server - tavily-search', 'description': "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'query', 'display_name': 'query', 'info': 'Search query', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'search_depth', 'display_name': 'search depth', 'info': "The depth of the search. It can be 'basic' or 'advanced'", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'basic', 'options': ['basic', 'advanced'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'topic', 'display_name': 'topic', 'info': 'The category of the search. This will determine which of our agents will be used for the search', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'general', 'options': ['general', 'news'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'days', 'display_name': 'days', 'info': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 3, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'time_range', 'display_name': 'time range', 'info': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'max_results', 'display_name': 'max results', 'info': 'The maximum number of search results to return', 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 10, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_images', 'display_name': 'include images', 'info': 'Include a list of query-related images in the response', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_image_descriptions', 'display_name': 'include image descriptions', 'info': 'Include a list of query-related images and their descriptions in the response', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_raw_content', 'display_name': 'include raw content', 'info': 'Include the cleaned and parsed HTML content of each search result', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_domains', 'display_name': 'include domains', 'info': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'exclude_domains', 'display_name': 'exclude domains', 'info': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'Generated_String', 'display_name': 'Generated_String', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_tavily_web_search_and_extraction_server_tavily-search', 'type': 'MCP', 'mcp_info': {'server_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'server_path': '', 'tool_name': 'tavily-search', 'input_schema': {'type': 'object', 'properties': {'query': {'type': 'string', 'description': 'Search query'}, 'search_depth': {'type': 'string', 'enum': ['basic', 'advanced'], 'description': "The depth of the search. It can be 'basic' or 'advanced'", 'default': 'basic'}, 'topic': {'type': 'string', 'enum': ['general', 'news'], 'description': 'The category of the search. This will determine which of our agents will be used for the search', 'default': 'general'}, 'days': {'type': 'number', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'default': 3}, 'time_range': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'enum': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y']}, 'max_results': {'type': 'number', 'description': 'The maximum number of search results to return', 'default': 10, 'minimum': 5, 'maximum': 20}, 'include_images': {'type': 'boolean', 'description': 'Include a list of query-related images in the response', 'default': False}, 'include_image_descriptions': {'type': 'boolean', 'description': 'Include a list of query-related images and their descriptions in the response', 'default': False}, 'include_raw_content': {'type': 'boolean', 'description': 'Include the cleaned and parsed HTML content of each search result', 'default': False}, 'include_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'default': []}, 'exclude_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'default': []}}, 'required': ['query']}, 'output_schema': {'properties': {'Generated_String': {'type': 'string', 'description': 'generated string from tavily', 'title': 'Generated_String'}}}}}, 'component_config': {}, 'mcp_metadata': {'server_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'server_path': '', 'tool_name': 'tavily-search', 'input_schema': {'type': 'object', 'properties': {'query': {'type': 'string', 'description': 'Search query'}, 'search_depth': {'type': 'string', 'enum': ['basic', 'advanced'], 'description': "The depth of the search. It can be 'basic' or 'advanced'", 'default': 'basic'}, 'topic': {'type': 'string', 'enum': ['general', 'news'], 'description': 'The category of the search. This will determine which of our agents will be used for the search', 'default': 'general'}, 'days': {'type': 'number', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'default': 3}, 'time_range': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'enum': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y']}, 'max_results': {'type': 'number', 'description': 'The maximum number of search results to return', 'default': 10, 'minimum': 5, 'maximum': 20}, 'include_images': {'type': 'boolean', 'description': 'Include a list of query-related images in the response', 'default': False}, 'include_image_descriptions': {'type': 'boolean', 'description': 'Include a list of query-related images and their descriptions in the response', 'default': False}, 'include_raw_content': {'type': 'boolean', 'description': 'Include the cleaned and parsed HTML content of each search result', 'default': False}, 'include_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'default': []}, 'exclude_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'default': []}}, 'required': ['query']}, 'output_schema': {'properties': {'Generated_String': {'type': 'string', 'description': 'generated string from tavily', 'title': 'Generated_String'}}}}}, {'node_id': 'MCP_Script_Generation_script_generate-1750057787782', 'node_type': 'MCP_Script_Generation_script_generate', 'node_label': 'Script Generation - script_generate', 'component_id': 'MCP_Script_Generation_script_generate-1750057787782', 'component_type': 'MCP_Script_Generation_script_generate', 'component_name': 'Script Generation - script_generate', 'component_definition': {'name': 'MCP_Script_Generation_script_generate', 'display_name': 'Script Generation - script_generate', 'description': 'Provide topic and keyword to generator Script', 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'topic', 'display_name': 'Topic', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'script_type', 'display_name': 'script type', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'TOPIC', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'keywords', 'display_name': 'keywords', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'video_type', 'display_name': 'video type', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'SHORT', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'link', 'display_name': 'Link', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'title', 'display_name': 'Title', 'output_type': 'string'}, {'name': 'script', 'display_name': 'Script', 'output_type': 'string'}, {'name': 'script_type', 'display_name': 'Script Type', 'output_type': 'string'}, {'name': 'video_type', 'display_name': 'Video Type', 'output_type': 'string'}, {'name': 'link', 'display_name': 'Link', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_script_generation_script_generate', 'type': 'MCP', 'mcp_info': {'server_id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_path': '', 'tool_name': 'script_generate', 'input_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}, 'output_schema': {'properties': {'title': {'type': 'string', 'description': 'Title of the generated script', 'title': 'Title'}, 'script': {'type': 'string', 'description': 'The generated script', 'title': 'Script'}, 'script_type': {'type': 'string', 'description': 'Type of the script', 'title': 'Script Type'}, 'video_type': {'type': 'string', 'description': 'The type of video', 'title': 'Video Type'}, 'link': {'type': 'string', 'format': 'uri', 'description': 'Optional link for the script', 'title': 'Link'}}}}}, 'component_config': {}, 'mcp_metadata': {'server_id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_path': '', 'tool_name': 'script_generate', 'input_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}, 'output_schema': {'properties': {'title': {'type': 'string', 'description': 'Title of the generated script', 'title': 'Title'}, 'script': {'type': 'string', 'description': 'The generated script', 'title': 'Script'}, 'script_type': {'type': 'string', 'description': 'Type of the script', 'title': 'Script Type'}, 'video_type': {'type': 'string', 'description': 'The type of video', 'title': 'Video Type'}, 'link': {'type': 'string', 'format': 'uri', 'description': 'Optional link for the script', 'title': 'Link'}}}}}]}}, 'style': {'opacity': 1}, 'width': 208, 'height': 218, 'selected': False, 'positionAbsolute': {'x': 420, 'y': -20}, 'dragging': False}, {'id': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'type': 'WorkflowNode', 'position': {'x': -60, 'y': -120}, 'data': {'label': 'Tavily Web Search and Extraction Server - tavily-search', 'type': 'mcp', 'originalType': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'definition': {'name': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', 'display_name': 'Tavily Web Search and Extraction Server - tavily-search', 'description': "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis.", 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'query', 'display_name': 'query', 'info': 'Search query', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'search_depth', 'display_name': 'search depth', 'info': "The depth of the search. It can be 'basic' or 'advanced'", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'basic', 'options': ['basic', 'advanced'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'topic', 'display_name': 'topic', 'info': 'The category of the search. This will determine which of our agents will be used for the search', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'general', 'options': ['general', 'news'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'days', 'display_name': 'days', 'info': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 3, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'time_range', 'display_name': 'time range', 'info': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'max_results', 'display_name': 'max results', 'info': 'The maximum number of search results to return', 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 10, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_images', 'display_name': 'include images', 'info': 'Include a list of query-related images in the response', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_image_descriptions', 'display_name': 'include image descriptions', 'info': 'Include a list of query-related images and their descriptions in the response', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_raw_content', 'display_name': 'include raw content', 'info': 'Include the cleaned and parsed HTML content of each search result', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'include_domains', 'display_name': 'include domains', 'info': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'exclude_domains', 'display_name': 'exclude domains', 'info': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'input_type': 'array', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'Generated_String', 'display_name': 'Generated_String', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_tavily_web_search_and_extraction_server_tavily-search', 'type': 'MCP', 'mcp_info': {'server_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'server_path': '', 'tool_name': 'tavily-search', 'input_schema': {'type': 'object', 'properties': {'query': {'type': 'string', 'description': 'Search query'}, 'search_depth': {'type': 'string', 'enum': ['basic', 'advanced'], 'description': "The depth of the search. It can be 'basic' or 'advanced'", 'default': 'basic'}, 'topic': {'type': 'string', 'enum': ['general', 'news'], 'description': 'The category of the search. This will determine which of our agents will be used for the search', 'default': 'general'}, 'days': {'type': 'number', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic", 'default': 3}, 'time_range': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics", 'enum': ['day', 'week', 'month', 'year', 'd', 'w', 'm', 'y']}, 'max_results': {'type': 'number', 'description': 'The maximum number of search results to return', 'default': 10, 'minimum': 5, 'maximum': 20}, 'include_images': {'type': 'boolean', 'description': 'Include a list of query-related images in the response', 'default': False}, 'include_image_descriptions': {'type': 'boolean', 'description': 'Include a list of query-related images and their descriptions in the response', 'default': False}, 'include_raw_content': {'type': 'boolean', 'description': 'Include the cleaned and parsed HTML content of each search result', 'default': False}, 'include_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'default': []}, 'exclude_domains': {'type': 'array', 'items': {'type': 'string'}, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'default': []}}, 'required': ['query']}, 'output_schema': {'properties': {'Generated_String': {'type': 'string', 'description': 'generated string from tavily', 'title': 'Generated_String'}}}}}, 'config': {}}, 'style': {'opacity': 1}, 'width': 210, 'height': 426, 'selected': False, 'positionAbsolute': {'x': -60, 'y': -120}, 'dragging': False}, {'id': 'MCP_Script_Generation_script_generate-1750057787782', 'type': 'WorkflowNode', 'position': {'x': -120, 'y': -400}, 'data': {'label': 'Script Generation - script_generate', 'type': 'mcp', 'originalType': 'MCP_Script_Generation_script_generate', 'definition': {'name': 'MCP_Script_Generation_script_generate', 'display_name': 'Script Generation - script_generate', 'description': 'Provide topic and keyword to generator Script', 'category': 'MCP', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'topic', 'display_name': 'Topic', 'info': '', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'script_type', 'display_name': 'script type', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'TOPIC', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'keywords', 'display_name': 'keywords', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'video_type', 'display_name': 'video type', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'SHORT', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'link', 'display_name': 'Link', 'info': '', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'title', 'display_name': 'Title', 'output_type': 'string'}, {'name': 'script', 'display_name': 'Script', 'output_type': 'string'}, {'name': 'script_type', 'display_name': 'Script Type', 'output_type': 'string'}, {'name': 'video_type', 'display_name': 'Video Type', 'output_type': 'string'}, {'name': 'link', 'display_name': 'Link', 'output_type': 'string'}], 'is_valid': True, 'path': 'mcp.mcp_script_generation_script_generate', 'type': 'MCP', 'mcp_info': {'server_id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_path': '', 'tool_name': 'script_generate', 'input_schema': {'$defs': {'Keywords': {'properties': {'time': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Time'}, 'objective': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Objective'}, 'audience': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Audience'}, 'gender': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Gender'}, 'tone': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Tone'}, 'speakers': {'anyOf': [{'items': {'type': 'string'}, 'type': 'array'}, {'type': 'null'}], 'default': None, 'title': 'Speakers'}}, 'title': 'Keywords', 'type': 'object'}, 'ScriptType': {'enum': ['VIDEO', 'TOPIC', 'SCRIPT', 'BLOG', 'AI'], 'title': 'ScriptType', 'type': 'string'}, 'VideoType': {'enum': ['SHORT', 'LONG'], 'title': 'VideoType', 'type': 'string'}}, 'properties': {'topic': {'title': 'Topic', 'type': 'string'}, 'script_type': {'$ref': '#/$defs/ScriptType', 'default': 'TOPIC'}, 'keywords': {'$ref': '#/$defs/Keywords'}, 'video_type': {'$ref': '#/$defs/VideoType', 'default': 'SHORT'}, 'link': {'anyOf': [{'format': 'uri', 'maxLength': 2083, 'minLength': 1, 'type': 'string'}, {'type': 'null'}], 'default': None, 'title': 'Link'}}, 'required': ['topic'], 'title': 'GenerateScriptInput', 'type': 'object'}, 'output_schema': {'properties': {'title': {'type': 'string', 'description': 'Title of the generated script', 'title': 'Title'}, 'script': {'type': 'string', 'description': 'The generated script', 'title': 'Script'}, 'script_type': {'type': 'string', 'description': 'Type of the script', 'title': 'Script Type'}, 'video_type': {'type': 'string', 'description': 'The type of video', 'title': 'Video Type'}, 'link': {'type': 'string', 'format': 'uri', 'description': 'Optional link for the script', 'title': 'Link'}}}}}, 'config': {}}, 'style': {'opacity': 1}, 'width': 210, 'height': 234, 'selected': False, 'positionAbsolute': {'x': -120, 'y': -400}, 'dragging': False}], 'edges': [{'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'AgenticAI-1750047131506', 'targetHandle': 'query', 'type': 'default', 'id': 'reactflow__edge-start-nodeflow-AgenticAI-1750047131506query', 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'sourceHandle': 'Generated_String', 'target': 'AgenticAI-1750047131506', 'targetHandle': 'tools', 'type': 'default', 'id': 'reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584Generated_String-AgenticAI-1750047131506tools', 'selected': False}, {'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'zIndex': 5}, 'source': 'MCP_Script_Generation_script_generate-1750057787782', 'sourceHandle': 'title', 'target': 'AgenticAI-1750047131506', 'targetHandle': 'tools', 'type': 'default', 'id': 'reactflow__edge-MCP_Script_Generation_script_generate-1750057787782title-AgenticAI-1750047131506tools'}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/6ad7d6e3-1fb6-47b1-a48a-74ac7c4ee001.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/6ad7d6e3-1fb6-47b1-a48a-74ac7c4ee001.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges']
[DEBUG] Number of nodes: 4
[DEBUG] Number of edges: 3
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=AgenticAI-1750047131506, type=agent, originalType=AgenticAI
[DEBUG] Node 2: id=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584, type=mcp, originalType=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search
[DEBUG] Node 3: id=MCP_Script_Generation_script_generate-1750057787782, type=mcp, originalType=MCP_Script_Generation_script_generate
[DEBUG] Edge 0: id=reactflow__edge-start-nodeflow-AgenticAI-1750047131506query, source=start-node, target=AgenticAI-1750047131506, sourceHandle=flow
[DEBUG] Edge 1: id=reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584Generated_String-AgenticAI-1750047131506tools, source=MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584, target=AgenticAI-1750047131506, sourceHandle=Generated_String
[DEBUG] Edge 2: id=reactflow__edge-MCP_Script_Generation_script_generate-1750057787782title-AgenticAI-1750047131506tools, source=MCP_Script_Generation_script_generate-1750057787782, target=AgenticAI-1750047131506, sourceHandle=title

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 4
   - Edges: 3
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ✅ Found tool node MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584 as separate visual node
   ✅ Tool nodes are already saved as separate visual components
   ✅ Skipping virtual node creation to avoid duplicates
   ✅ Using existing tool nodes and edges
   🔧 CONFIGURING TOOL DATA FLOW FOR EXISTING NODES...
         - Configured 2 tools for AgenticAI node AgenticAI-1750047131506
   ✅ Tool data flow configured for existing tool nodes
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(AgenticAI-1750047131506): node_type='agent', original_type='AgenticAI', result=False
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False
[DEBUG] is_conditional_node(MCP_Script_Generation_script_generate-1750057787782): node_type='mcp', original_type='MCP_Script_Generation_script_generate', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - AgenticAI: 1
   - MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search: 1
   - MCP_Script_Generation_script_generate: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
   ℹ️  Allowing multiple connections to AgenticAI-1750047131506.tools (tools handle accepts multiple connections)
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - AgenticAI-1750047131506
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 3
   - Edge mappings: 3
   - All nodes: 4
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 3 nodes
   - Grouped into 1 levels

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/4: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/4: AgenticAI-1750047131506
   Type: AgenticAI (agent)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(AgenticAI-1750047131506): node_type='agent', original_type='AgenticAI', result=False
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 3/4: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584
   Type: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False
   🔧 Fixed MCP tool_name: tavily-search
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 4/4: MCP_Script_Generation_script_generate-1750057787782
   Type: MCP_Script_Generation_script_generate (mcp)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MCP_Script_Generation_script_generate-1750057787782): node_type='mcp', original_type='MCP_Script_Generation_script_generate', result=False
   🔧 Fixed MCP tool_name: script_generate
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (3 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - All component nodes processed: 3 ['AgenticAI-1750047131506', 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'MCP_Script_Generation_script_generate-1750057787782']
   - Final transition_nodes count: 3

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MCP_Script_Generation_script_generate-1750057787782', 'AgenticAI-1750047131506', 'MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584']

   📦 Processing node: MCP_Script_Generation_script_generate-1750057787782
      Type: MCP_Script_Generation_script_generate (mcp)
[DEBUG] is_conditional_node(MCP_Script_Generation_script_generate-1750057787782): node_type='mcp', original_type='MCP_Script_Generation_script_generate', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Script_Generation_script_generate-1750057787782): node_type='mcp', result=False
[DEBUG] is_conditional_node(AgenticAI-1750047131506): node_type='agent', original_type='AgenticAI', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-MCP_Script_Generation_script_generate-1750057787782
         - Sequence: 1
         - Execution Type: MCP
         - Tools: 1
         - Input Data: 0
         - Output Data: 1

   📦 Processing node: AgenticAI-1750047131506
      Type: AgenticAI (agent)
[DEBUG] is_conditional_node(AgenticAI-1750047131506): node_type='agent', original_type='AgenticAI', result=False
      Is conditional: False
[DEBUG] is_output_node(AgenticAI-1750047131506): node_type='agent', result=False

   📦 Processing node: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584
      Type: MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search (mcp)
[DEBUG] is_conditional_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584): node_type='mcp', original_type='MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search', result=False
      Is conditional: False
[DEBUG] is_output_node(MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584): node_type='mcp', result=False
[DEBUG] is_conditional_node(AgenticAI-1750047131506): node_type='agent', original_type='AgenticAI', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584
         - Sequence: 3
         - Execution Type: MCP
         - Tools: 1
         - Input Data: 0
         - Output Data: 1

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 3
   - Total transitions created: 3
   - Conditional transitions: 0
   - Regular transitions: 2

⚙️  REGULAR TRANSITIONS CREATED:
   - MCP_Script_Generation_script_generate-1750057787782: 1 tools
   - MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584: 1 tools

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'AgenticAI', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'AgenticAI', 'input_schema': {'predefined_fields': [{'field_name': 'model_provider', 'data_type': {'type': 'string', 'description': 'The AI model provider to use.'}, 'required': False}, {'field_name': 'base_url', 'data_type': {'type': 'string', 'description': 'Base URL for the API (leave empty for default provider URL).'}, 'required': False}, {'field_name': 'api_key', 'data_type': {'type': 'string', 'description': 'API key for the model provider. Can be entered directly or referenced from secure storage.'}, 'required': False}, {'field_name': 'model_name', 'data_type': {'type': 'string', 'description': 'Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.'}, 'required': False}, {'field_name': 'temperature', 'data_type': {'type': 'number', 'description': 'Controls randomness: 0 is deterministic, higher values are more random.'}, 'required': False}, {'field_name': 'description', 'data_type': {'type': 'string', 'description': 'Description of the agent for UI display.'}, 'required': False}, {'field_name': 'execution_type', 'data_type': {'type': 'string', 'description': 'Determines if agent handles single response or multi-turn conversation.'}, 'required': False}, {'field_name': 'query', 'data_type': {'type': 'string', 'description': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.'}, 'required': True}, {'field_name': 'system_message', 'data_type': {'type': 'string', 'description': 'System prompt/instructions for the agent. If empty, will use default based on query.'}, 'required': False}, {'field_name': 'termination_condition', 'data_type': {'type': 'string', 'description': 'Defines when multi-turn conversations should end. Required for interactive execution type.'}, 'required': False}, {'field_name': 'max_tokens', 'data_type': {'type': 'number', 'description': 'Maximum response length in tokens.'}, 'required': False}, {'field_name': 'input_variables', 'data_type': {'type': 'object', 'description': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'tools', 'data_type': {'type': 'string', 'description': 'Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.'}, 'required': False}, {'field_name': 'memory', 'data_type': {'type': 'string', 'description': 'Connect a memory object from another node.'}, 'required': False}, {'field_name': 'autogen_agent_type', 'data_type': {'type': 'string', 'description': 'The type of AutoGen agent to create internally.'}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'final_answer', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'intermediate_steps', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'updated_memory', 'data_type': {'type': 'string', 'description': '', 'format': 'datetime'}}, {'field_name': 'error', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}]}}]}, {'id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'tavily-search', 'input_schema': {'predefined_fields': [{'field_name': 'query', 'data_type': {'type': 'string', 'description': 'Search query'}, 'required': True}, {'field_name': 'search_depth', 'data_type': {'type': 'string', 'description': "The depth of the search. It can be 'basic' or 'advanced'"}, 'required': False}, {'field_name': 'topic', 'data_type': {'type': 'string', 'description': 'The category of the search. This will determine which of our agents will be used for the search'}, 'required': False}, {'field_name': 'days', 'data_type': {'type': 'number', 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic"}, 'required': False}, {'field_name': 'time_range', 'data_type': {'type': 'string', 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics"}, 'required': False}, {'field_name': 'max_results', 'data_type': {'type': 'number', 'description': 'The maximum number of search results to return'}, 'required': False}, {'field_name': 'include_images', 'data_type': {'type': 'boolean', 'description': 'Include a list of query-related images in the response'}, 'required': False}, {'field_name': 'include_image_descriptions', 'data_type': {'type': 'boolean', 'description': 'Include a list of query-related images and their descriptions in the response'}, 'required': False}, {'field_name': 'include_raw_content', 'data_type': {'type': 'boolean', 'description': 'Include the cleaned and parsed HTML content of each search result'}, 'required': False}, {'field_name': 'include_domains', 'data_type': {'type': 'array', 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site', 'items': {'type': 'string'}}, 'required': False}, {'field_name': 'exclude_domains', 'data_type': {'type': 'array', 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site', 'items': {'type': 'string'}}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'Generated_String', 'data_type': {'type': 'string', 'description': 'generated string from tavily', 'format': 'percentage'}}]}}]}, {'id': '0dc83245-794f-405d-8814-7771260d3c60', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'script_generate', 'input_schema': {'predefined_fields': [{'field_name': 'topic', 'data_type': {'type': 'string', 'description': ''}, 'required': True}, {'field_name': 'script_type', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'keywords', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'video_type', 'data_type': {'type': 'string', 'description': ''}, 'required': False}, {'field_name': 'link', 'data_type': {'type': 'string', 'description': ''}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'title', 'data_type': {'type': 'string', 'description': 'Title of the generated script', 'format': 'string'}}, {'field_name': 'script', 'data_type': {'type': 'string', 'description': 'The generated script', 'format': 'string'}}, {'field_name': 'script_type', 'data_type': {'type': 'string', 'description': 'Type of the script', 'format': 'string'}}, {'field_name': 'video_type', 'data_type': {'type': 'string', 'description': 'The type of video', 'format': 'video'}}, {'field_name': 'link', 'data_type': {'type': 'string', 'description': 'Optional link for the script', 'format': 'uri'}}]}}]}], 'transitions': [{'id': 'transition-MCP_Script_Generation_script_generate-1750057787782', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'MCP', 'node_info': {'node_id': '0dc83245-794f-405d-8814-7771260d3c60', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'script_generate', 'tool_params': {'items': [{'field_name': 'topic', 'data_type': 'string', 'field_value': None}, {'field_name': 'script_type', 'data_type': 'string', 'field_value': None}, {'field_name': 'keywords', 'data_type': 'string', 'field_value': None}, {'field_name': 'video_type', 'data_type': 'string', 'field_value': None}, {'field_name': 'link', 'data_type': 'string', 'field_value': None}]}}], 'input_data': [], 'output_data': [{'to_transition_id': 'transition-AgenticAI-1750047131506', 'target_node_id': 'AI Agent Executor', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'title', 'result_path': 'title', 'edge_id': 'reactflow__edge-MCP_Script_Generation_script_generate-1750057787782title-AgenticAI-1750047131506tools'}]}}]}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'topic', 'handle_name': 'Topic', 'data_type': 'string', 'required': True, 'description': ''}, {'handle_id': 'script_type', 'handle_name': 'script type', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'keywords', 'handle_name': 'keywords', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'video_type', 'handle_name': 'video type', 'data_type': 'string', 'required': False, 'description': ''}, {'handle_id': 'link', 'handle_name': 'Link', 'data_type': 'string', 'required': False, 'description': ''}], 'output_handles': [{'handle_id': 'title', 'handle_name': 'Title', 'data_type': 'string', 'description': ''}, {'handle_id': 'script', 'handle_name': 'Script', 'data_type': 'string', 'description': ''}, {'handle_id': 'script_type', 'handle_name': 'Script Type', 'data_type': 'string', 'description': ''}, {'handle_id': 'video_type', 'handle_name': 'Video Type', 'data_type': 'string', 'description': ''}, {'handle_id': 'link', 'handle_name': 'Link', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'title': 'title', 'script': 'script', 'script_type': 'script_type', 'video_type': 'video_type', 'link': 'link'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.title', 'output_data.title', 'response.title', 'data.title', 'result.script', 'output_data.script', 'response.script', 'data.script', 'result.script_type', 'output_data.script_type', 'response.script_type', 'data.script_type', 'result.video_type', 'output_data.video_type', 'response.video_type', 'data.video_type', 'result.link', 'output_data.link', 'response.link', 'data.link', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'title'}}, 'approval_required': False, 'end': False}, {'id': 'transition-AgenticAI-1750047131506', 'sequence': 2, 'transition_type': 'initial', 'execution_type': 'agent', 'node_info': {'node_id': 'AgenticAI', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'AgenticAI', 'tool_params': {'items': [{'field_name': 'agent_type', 'data_type': 'string', 'field_value': 'component'}, {'field_name': 'execution_type', 'data_type': 'string', 'field_value': 'response'}, {'field_name': 'query', 'data_type': 'string', 'field_value': ''}, {'field_name': 'agent_config', 'data_type': 'object', 'field_value': {}}]}}], 'input_data': [{'from_transition_id': 'transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'source_node_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'source_handle_id': 'Generated_String', 'target_handle_id': 'tools', 'edge_id': 'reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584Generated_String-AgenticAI-1750047131506tools'}]}, {'from_transition_id': 'transition-MCP_Script_Generation_script_generate-1750057787782', 'source_node_id': '0dc83245-794f-405d-8814-7771260d3c60', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-MCP_Script_Generation_script_generate-1750057787782', 'source_handle_id': 'title', 'target_handle_id': 'tools', 'edge_id': 'reactflow__edge-MCP_Script_Generation_script_generate-1750057787782title-AgenticAI-1750047131506tools'}]}], 'output_data': []}, 'result_resolution': {'node_type': 'agent', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'query', 'handle_name': 'Query/Objective', 'data_type': 'string', 'required': True, 'description': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.'}, {'handle_id': 'input_variables', 'handle_name': 'Input Variables', 'data_type': 'object', 'required': False, 'description': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.'}, {'handle_id': 'tools', 'handle_name': 'Tools', 'data_type': 'string', 'required': False, 'description': 'Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.'}, {'handle_id': 'memory', 'handle_name': 'Memory Object', 'data_type': 'string', 'required': False, 'description': 'Connect a memory object from another node.'}], 'output_handles': [{'handle_id': 'final_answer', 'handle_name': 'Final Answer', 'data_type': 'string', 'description': ''}, {'handle_id': 'intermediate_steps', 'handle_name': 'Intermediate Steps', 'data_type': 'string', 'description': ''}, {'handle_id': 'updated_memory', 'handle_name': 'Updated Memory', 'data_type': 'string', 'description': ''}, {'handle_id': 'error', 'handle_name': 'Error', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'final_answer': 'final_answer', 'intermediate_steps': 'intermediate_steps', 'updated_memory': 'updated_memory', 'error': 'error'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.final_answer', 'output_data.final_answer', 'response.final_answer', 'data.final_answer', 'result.intermediate_steps', 'output_data.intermediate_steps', 'response.intermediate_steps', 'data.intermediate_steps', 'result.updated_memory', 'output_data.updated_memory', 'response.updated_memory', 'data.updated_memory', 'result.error', 'output_data.error', 'response.error', 'data.error', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'final_answer'}}, 'approval_required': False, 'end': True}, {'id': 'transition-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584', 'sequence': 3, 'transition_type': 'standard', 'execution_type': 'MCP', 'node_info': {'node_id': 'fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'tavily-search', 'tool_params': {'items': [{'field_name': 'query', 'data_type': 'string', 'field_value': None}, {'field_name': 'search_depth', 'data_type': 'string', 'field_value': None}, {'field_name': 'topic', 'data_type': 'string', 'field_value': None}, {'field_name': 'days', 'data_type': 'string', 'field_value': None}, {'field_name': 'time_range', 'data_type': 'string', 'field_value': None}, {'field_name': 'max_results', 'data_type': 'string', 'field_value': None}, {'field_name': 'include_images', 'data_type': 'boolean', 'field_value': None}, {'field_name': 'include_image_descriptions', 'data_type': 'boolean', 'field_value': None}, {'field_name': 'include_raw_content', 'data_type': 'boolean', 'field_value': None}, {'field_name': 'include_domains', 'data_type': 'string', 'field_value': None}, {'field_name': 'exclude_domains', 'data_type': 'string', 'field_value': None}]}}], 'input_data': [], 'output_data': [{'to_transition_id': 'transition-AgenticAI-1750047131506', 'target_node_id': 'AI Agent Executor', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'Generated_String', 'result_path': 'Generated_String', 'edge_id': 'reactflow__edge-MCP_Tavily_Web_Search_and_Extraction_Server_tavily-search-1750057756584Generated_String-AgenticAI-1750047131506tools'}]}}]}, 'result_resolution': {'node_type': 'mcp', 'expected_result_structure': 'dynamic', 'handle_registry': {'input_handles': [{'handle_id': 'query', 'handle_name': 'query', 'data_type': 'string', 'required': True, 'description': 'Search query'}, {'handle_id': 'search_depth', 'handle_name': 'search depth', 'data_type': 'string', 'required': False, 'description': "The depth of the search. It can be 'basic' or 'advanced'"}, {'handle_id': 'topic', 'handle_name': 'topic', 'data_type': 'string', 'required': False, 'description': 'The category of the search. This will determine which of our agents will be used for the search'}, {'handle_id': 'days', 'handle_name': 'days', 'data_type': 'string', 'required': False, 'description': "The number of days back from the current date to include in the search results. This specifies the time frame of data to be retrieved. Please note that this feature is only available when using the 'news' search topic"}, {'handle_id': 'time_range', 'handle_name': 'time range', 'data_type': 'string', 'required': False, 'description': "The time range back from the current date to include in the search results. This feature is available for both 'general' and 'news' search topics"}, {'handle_id': 'max_results', 'handle_name': 'max results', 'data_type': 'string', 'required': False, 'description': 'The maximum number of search results to return'}, {'handle_id': 'include_images', 'handle_name': 'include images', 'data_type': 'boolean', 'required': False, 'description': 'Include a list of query-related images in the response'}, {'handle_id': 'include_image_descriptions', 'handle_name': 'include image descriptions', 'data_type': 'boolean', 'required': False, 'description': 'Include a list of query-related images and their descriptions in the response'}, {'handle_id': 'include_raw_content', 'handle_name': 'include raw content', 'data_type': 'boolean', 'required': False, 'description': 'Include the cleaned and parsed HTML content of each search result'}, {'handle_id': 'include_domains', 'handle_name': 'include domains', 'data_type': 'string', 'required': False, 'description': 'A list of domains to specifically include in the search results, if the user asks to search on specific sites set this to the domain of the site'}, {'handle_id': 'exclude_domains', 'handle_name': 'exclude domains', 'data_type': 'string', 'required': False, 'description': 'List of domains to specifically exclude, if the user asks to exclude a domain set this to the domain of the site'}], 'output_handles': [{'handle_id': 'Generated_String', 'handle_name': 'Generated_String', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'Generated_String': 'Generated_String'}, 'dynamic_discovery': {'enabled': True, 'fallback_patterns': ['result.Generated_String', 'output_data.Generated_String', 'response.Generated_String', 'data.Generated_String', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': False, 'supports_nested_results': True, 'requires_dynamic_discovery': True, 'primary_output_handle': 'Generated_String'}}, 'approval_required': False, 'end': False}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/e3cce910-bd8f-4396-9227-1822e583277b.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/e3cce910-bd8f-4396-9227-1822e583277b.json
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-06-16 12:39:58 [info     ] Set is_updated=True for workflow 4e06cd30-4f3e-486e-9443-91d195a4288a due to version-relevant changes
2025-06-16 12:39:59 [info     ] Checking derived workflow update conditions: template_relevant_fields_changed=True, workflow.visibility=WorkflowVisibilityEnum.PRIVATE, is_public=False
2025-06-16 12:39:59 [info     ] Skipping derived workflow update for workflow 4e06cd30-4f3e-486e-9443-91d195a4288a
