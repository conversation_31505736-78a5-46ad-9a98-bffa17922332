import React, { useState, use<PERSON><PERSON>back, useRef, useEffect } from "react";
import React<PERSON>low, {
  ReactFlowProvider,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  Node,
  Edge,
  OnNodesChange,
  OnEdgesChange,
  OnConnect,
  NodeChange,
  EdgeChange,
  Connection,
  Background,
  Controls,
  MiniMap,
  useReactFlow,
  NodeOrigin,
  BackgroundVariant,
  Panel,
} from "reactflow";
import { Trash2, Co<PERSON>, Clipboard } from "lucide-react";

import { WorkflowNodeData, ComponentDefinition } from "@/types";
import { InspectorPanel } from "@/components/inspector";
import WorkflowNode from "@/components/nodes/WorkflowNode";
import DeletableEdge from "@/components/edges/DeletableEdge";
import { getConnectedNodesWithToolConnections } from "@/lib/validation/toolConnectionFlow";
import { isToolHandle } from "@/utils/toolConnectionUtils";
// Removed Button and Tooltip imports to fix infinite update loop

// Style for minimap with more modern colors
const nodeColor = (node: Node) => {
  const data = node.data as WorkflowNodeData;
  const category = data?.definition?.category?.toLowerCase() || "";

  // Color nodes based on their category
  switch (category) {
    case "io":
      return "var(--chart-1)";
    case "data":
      return "var(--chart-2)";
    case "processing":
      return "var(--chart-3)";
    case "api":
      return "var(--chart-4)";
    case "control flow":
      return "var(--chart-5)";
    default:
      return "var(--primary)";
  }
};

// Create a StartNode as the initial node
const createStartNode = (): Node<WorkflowNodeData> => {
  return {
    id: "start-node",
    type: "WorkflowNode",
    position: { x: 100, y: 100 },
    data: {
      label: "Start",
      type: "component",
      originalType: "StartNode",
      definition: {
        name: "StartNode",
        display_name: "Start",
        description:
          "The starting point for all workflows. Only nodes connected to this node will be executed.",
        category: "Input/Output",
        icon: "Play",
        beta: false,
        inputs: [],
        outputs: [
          {
            name: "flow",
            display_name: "Flow",
            output_type: "Any",
          },
        ],
        is_valid: true,
        path: "components.io.start_node",
      },
      config: {
        collected_parameters: {},
      },
    },
  };
};

const initialNodes: Node<WorkflowNodeData>[] = [createStartNode()];
const initialEdges: Edge[] = [];

// Set node origin to center for better drag/drop placement
const nodeOrigin: NodeOrigin = [0.5, 0.5];

// Define nodeTypes outside the component to prevent re-creation on each render
const nodeTypes = {
  WorkflowNode: WorkflowNode, // Register custom node with a key
  // You could add other specialized custom nodes here if needed later
};

// Define edgeTypes outside the component to prevent re-creation on each render
const edgeTypes = {
  default: DeletableEdge,
};

interface FlowCanvasProps {
  onFlowChange: (nodes: Node<WorkflowNodeData>[], edges: Edge[]) => void;
  initialNodes?: Node<WorkflowNodeData>[];
  initialEdges?: Edge[];
}

function FlowCanvas({
  onFlowChange,
  initialNodes: propInitialNodes,
  initialEdges: propInitialEdges,
}: FlowCanvasProps) {
  const reactFlowWrapper = useRef<HTMLDivElement>(null); // Ref for drop area
  const { screenToFlowPosition, fitView } = useReactFlow(); // Hook to convert screen coords to flow coords

  const [nodes, setNodes] = useState<Node<WorkflowNodeData>[]>(propInitialNodes || initialNodes);
  // Ensure all edges have the correct type when initializing
  const [edges, setEdges] = useState<Edge[]>(() => {
    const initialEdgeState = propInitialEdges || initialEdges;
    return initialEdgeState.map((edge) => ({
      ...edge,
      type: edge.type || "default",
    }));
  });

  // Update nodes and edges when initialNodes or initialEdges change
  useEffect(() => {
    if (propInitialNodes) {
      console.log("Updating nodes from props:", propInitialNodes);

      // Check if there's a StartNode in the initial nodes
      const hasStartNode = propInitialNodes.some((node) => node.data.originalType === "StartNode");

      if (!hasStartNode) {
        // If no StartNode, add one
        console.log("Adding StartNode to initial nodes");
        setNodes([createStartNode(), ...propInitialNodes]);
      } else {
        setNodes(propInitialNodes);
      }
    }
  }, [propInitialNodes]);

  useEffect(() => {
    if (propInitialEdges) {
      console.log("Updating edges from props:", propInitialEdges);
      // Ensure all edges have the correct type
      const edgesWithType = propInitialEdges.map((edge) => ({
        ...edge,
        type: edge.type || "default",
      }));
      console.log("Edges with type:", edgesWithType);
      setEdges(edgesWithType);
    }
  }, [propInitialEdges]);
  const [selectedNode, setSelectedNode] = useState<Node<WorkflowNodeData> | null>(null);

  // Memoize the flow change to prevent unnecessary updates
  const memoizedNodes = useRef(nodes);
  const memoizedEdges = useRef(edges);

  // Add a synchronization check to ensure React Flow state is properly initialized
  useEffect(() => {
    // Log the current state for debugging
    console.log("[WorkflowCanvas] Current state:", {
      nodesLength: nodes.length,
      edgesLength: edges.length,
      hasStartNode: nodes.some((node) => node.data.originalType === "StartNode"),
    });

    // Ensure there's always a StartNode
    if (nodes.length > 0 && !nodes.some((node) => node.data.originalType === "StartNode")) {
      console.warn("[WorkflowCanvas] No StartNode found in current nodes. Adding one.");
      setNodes((prevNodes) => [createStartNode(), ...prevNodes]);
    }

    // If nodes array is empty but should have nodes, reinitialize with at least a StartNode
    if (nodes.length === 0) {
      console.warn("[WorkflowCanvas] Nodes array is empty. Reinitializing with StartNode.");
      setNodes([createStartNode()]);
    }
  }, [nodes, setNodes]);

  // Track if we're currently editing a field in the inspector panel
  const isEditingFieldRef = useRef(false);

  // Function to set editing state - will be passed to InspectorPanel
  const setIsEditingField = useCallback((isEditing: boolean) => {
    isEditingFieldRef.current = isEditing;
  }, []);

  useEffect(() => {
    // Only call onFlowChange if nodes or edges have actually changed
    // AND we're not currently editing a field in the inspector panel
    if (
      (nodes !== memoizedNodes.current || edges !== memoizedEdges.current) &&
      !isEditingFieldRef.current
    ) {
      memoizedNodes.current = nodes;
      memoizedEdges.current = edges;

      // Log the flow change for debugging - only in development mode
      if (process.env.NODE_ENV === "development") {
        console.log("[WorkflowCanvas] Flow changed:", {
          nodesLength: nodes.length,
          edgesLength: edges.length,
          nodeIds: nodes.map((n) => n.id).join(", "),
          edges: edges.map((e) => ({ id: e.id, type: e.type, source: e.source, target: e.target })),
        });
      }

      onFlowChange(nodes, edges);
    } else if (isEditingFieldRef.current) {
      // Still update our memoized refs even when editing
      memoizedNodes.current = nodes;
      memoizedEdges.current = edges;

      // But don't trigger the onFlowChange callback
      if (process.env.NODE_ENV === "development") {
        console.log("[WorkflowCanvas] Skipping flow change notification during field editing");
      }
    }
  }, [nodes, edges, onFlowChange]);

  // Fit view when nodes change (especially on initial load)
  useEffect(() => {
    if (nodes.length > 0) {
      setTimeout(() => {
        fitView({ padding: 0.2 });
      }, 100); // Small delay to ensure nodes are rendered
    }
  }, [nodes, fitView]);

  // Function to get all connected nodes from a source node
  const getConnectedNodes = useCallback(
    (sourceNodeId: string, edgesList: Edge[], visited = new Set<string>()): Set<string> => {
      if (visited.has(sourceNodeId)) return visited;

      visited.add(sourceNodeId);

      // Find all edges where this node is the source
      const outgoingEdges = edgesList.filter((edge) => edge.source === sourceNodeId);

      // Recursively get connected nodes
      for (const edge of outgoingEdges) {
        getConnectedNodes(edge.target, edgesList, visited);
      }

      return visited;
    },
    [],
  );

  // Function to identify nodes connected to the StartNode (including tool connections)
  const getNodesConnectedToStartNode = useCallback((): Set<string> => {
    // Find the StartNode
    const startNode = nodes.find((node) => node.data.originalType === "StartNode");
    if (!startNode) return new Set<string>();

    // Get all nodes connected to the StartNode including tool connections
    return getConnectedNodesWithToolConnections(nodes, edges, startNode.id);
  }, [nodes, edges]);

  // Update node styling to indicate disconnected nodes - with memoization to prevent infinite loops
  const updateNodeStyling = useCallback(() => {
    // Skip if there are no nodes
    if (nodes.length === 0) return;

    // Get connected nodes
    const connectedNodes = getNodesConnectedToStartNode();

    // Update node styling
    setNodes((nds) => {
      // First check if we actually need to update any nodes
      const needsUpdate = nds.some((node) => {
        if (node.data.originalType === "StartNode") return false;

        const isConnected = connectedNodes.has(node.id);
        const currentOpacity = node.style?.opacity;
        const currentBorder = node.style?.border;

        return (
          (isConnected && currentOpacity !== 1) ||
          (!isConnected && currentOpacity !== 0.5) ||
          (isConnected && currentBorder !== undefined) ||
          (!isConnected && currentBorder !== "1px dashed #3F72AF")
        );
      });

      // If no updates needed, return the original nodes
      if (!needsUpdate) return nds;

      // Otherwise, update the nodes
      return nds.map((node) => {
        // Skip the StartNode itself
        if (node.data.originalType === "StartNode") return node;

        // Check if this node is connected to the StartNode
        const isConnected = connectedNodes.has(node.id);

        // Update node styling
        return {
          ...node,
          style: {
            ...node.style,
            opacity: isConnected ? 1 : 0.5,
            border: isConnected ? undefined : "1px dashed #3F72AF",
          },
        };
      });
    });
  }, [nodes, edges, getNodesConnectedToStartNode]);

  // Call the update function when nodes or edges change
  useEffect(() => {
    updateNodeStyling();
  }, [updateNodeStyling]);
  const onNodesChange: OnNodesChange = useCallback(
    (changes: NodeChange[]) => {
      // Filter out any changes that would delete the StartNode
      const filteredChanges = changes.filter((change) => {
        if (change.type === "remove") {
          const nodeId = change.id;
          const node = nodes.find((n) => n.id === nodeId);
          if (node && node.data.originalType === "StartNode") {
            console.log("Prevented deletion of StartNode");
            return false;
          }
        }
        return true;
      });

      setNodes((nds) => applyNodeChanges(filteredChanges, nds));
    },
    [nodes],
  );
  const onEdgesChange: OnEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      // Handle edge removal for tool connections
      changes.forEach((change) => {
        if (change.type === "remove") {
          const edgeToRemove = edges.find(e => e.id === change.id);
          if (edgeToRemove && edgeToRemove.targetHandle && isToolHandle(edgeToRemove.targetHandle)) {
            console.log(`[WorkflowCanvas] Removing tool connection: ${edgeToRemove.source} -> ${edgeToRemove.target} (${edgeToRemove.targetHandle})`);
            
            // Update the target AgenticAI node's configuration to remove tool connection data
            setNodes((nds) => nds.map((node) => {
              if (node.id === edgeToRemove.target && node.data.originalType === "AgenticAI") {
                const currentConfig = node.data.config || {};

                // Remove tool from simplified config.tools array
                const updatedTools = (currentConfig.tools || []).filter(
                  (tool: any) => tool.node_id !== edgeToRemove.source
                );

                console.log(`[WorkflowCanvas] Updated AgenticAI node ${edgeToRemove.target} tools after removal:`, updatedTools);

                return {
                  ...node,
                  data: {
                    ...node.data,
                    config: {
                      ...currentConfig,
                      tools: updatedTools
                    }
                  }
                };
              }
              return node;
            }));
          }
        }
      });
      
      // Apply the edge changes
      setEdges((eds) => applyEdgeChanges(changes, eds));
    },
    [edges, setNodes, setEdges],
  );
  const onConnect: OnConnect = useCallback(
    (connection: Connection) => {
      // Create the edge with default type
      const newEdge = { ...connection, type: "default" };
      
      // Check if this is a tool connection
      if (connection.targetHandle && isToolHandle(connection.targetHandle)) {
        console.log(`[WorkflowCanvas] Creating tool connection: ${connection.source} -> ${connection.target} (${connection.targetHandle})`);

        // IMPORTANT: Tool connections are now generated from edges during save/execution
        // This config-based approach is kept for backward compatibility and immediate UI feedback
        // The actual tool connections used during execution are generated from the visual edges

        // Update the target AgenticAI node's configuration to store tool connection data
        setNodes((nds) => nds.map((node) => {
          if (node.id === connection.target && node.data.originalType === "AgenticAI") {
            const sourceNode = nds.find(n => n.id === connection.source);
            if (sourceNode) {
              const currentConfig = node.data.config || {};

              // Create new tool connection data
              const newToolConnection = {
                node_id: connection.source,
                node_type: sourceNode.data.originalType || sourceNode.data.type,
                node_label: sourceNode.data.label,
                component_id: connection.source,
                component_type: sourceNode.data.originalType || sourceNode.data.type,
                component_name: sourceNode.data.label,
                component_definition: sourceNode.data.definition,
                component_config: sourceNode.data.config || {},
                // Add MCP metadata if available
                ...(sourceNode.data.definition?.mcp_info && {
                  mcp_metadata: sourceNode.data.definition.mcp_info
                })
              };

              // Handle simplified format (config.tools)
              let updatedTools = [...(currentConfig.tools || [])];

              // Check if this specific node is already connected
              const existingToolIndex = updatedTools.findIndex(
                (tool: any) => tool.node_id === connection.source
              );

              if (existingToolIndex >= 0) {
                // Update existing connection
                updatedTools[existingToolIndex] = newToolConnection;
                console.log(`[WorkflowCanvas] Updated existing tool connection`);
              } else {
                // Add new connection
                updatedTools.push(newToolConnection);
                console.log(`[WorkflowCanvas] Added new tool connection. Total tools: ${updatedTools.length}`);
              }

              console.log(`[WorkflowCanvas] Updated AgenticAI node ${connection.target} with tools:`, updatedTools);

              return {
                ...node,
                data: {
                  ...node.data,
                  config: {
                    ...currentConfig,
                    tools: updatedTools  // Simplified format only
                  }
                }
              };
            }
          }
          return node;
        }));
      }
      
      // Add the edge
      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setNodes, setEdges],
  );

  // Drag and Drop handler
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      if (!reactFlowWrapper.current) return;

      const dataString = event.dataTransfer.getData("application/reactflow");
      if (!dataString) return; // Exit if no valid data transferred

      const { nodeType: originalNodeType, definition } = JSON.parse(dataString) as {
        nodeType: string;
        definition: ComponentDefinition;
      };
      console.log("Dropped Node Definition Outputs:", definition.outputs);

      // Check if the drop position is valid
      const bounds = reactFlowWrapper.current.getBoundingClientRect();

      console.log("Event ClientX:", event.clientX, "ClientY:", event.clientY);
      console.log("Canvas Bounds:", bounds);

      const position = screenToFlowPosition({
        x: event.clientX - bounds.left, // Adjust for wrapper offset
        y: event.clientY - bounds.top,
      });

      console.log("Computed position:", position);

      // Determine component type
      const isMCPComponent =
        definition.type === "MCP" ||
        (definition.path && definition.path.includes("mcp_marketplace")) ||
        originalNodeType === "MCPToolsComponent";

      // Determine if this is an agent component
      const isAgentComponent =
        originalNodeType === "AgenticAI" ||
        (definition.path && definition.path.includes("components.ai.agenticai")) ||
        (definition.category === "AI" && definition.name === "AgenticAI");

      // Set the appropriate node type
      let nodeType = "component"; // default
      if (isMCPComponent) {
        nodeType = "mcp";
      } else if (isAgentComponent) {
        nodeType = "agent";
      }

      const newNode: Node<WorkflowNodeData> = {
        id: `${originalNodeType}-${+new Date()}`, // Simple unique ID generation
        type: "WorkflowNode", // Always use WorkflowNode for all components
        position,
        data: {
          label: definition.display_name, // Default label from definition
          type: nodeType, // Set node.data.type as enum value
          originalType: originalNodeType, // Keep the original type for reference
          definition: definition, // Store definition for inspector
          config: {}, // Initialize empty config object
        },
        // Apply node origin through ReactFlow props instead
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [screenToFlowPosition, setNodes], // Include screenToFlowPosition in dependencies
  );

  // Node click handler
  const onNodeClick = useCallback((_: React.MouseEvent, node: Node<WorkflowNodeData>) => {
    setSelectedNode(node);
    console.log("Selected Node:", node); // Debugging
  }, []);

  // Close inspector
  const handleCloseInspector = () => {
    setSelectedNode(null);
  };

  // Delete Node Handler
  const handleDeleteNode = useCallback(
    (nodeId: string) => {
      // Check if the node is a StartNode
      const node = nodes.find((n) => n.id === nodeId);
      if (node && node.data.originalType === "StartNode") {
        console.log("Cannot delete StartNode");
        return; // Prevent deletion of StartNode
      }

      // Clean up StartNode collected parameters for the deleted node
      const startNode = nodes.find((n) => n.data.originalType === "StartNode");
      if (startNode && startNode.data.config && startNode.data.config.collected_parameters) {
        console.log(`Cleaning up StartNode parameters for deleted node ${nodeId}`);

        // Create a new collected_parameters object without the deleted node's parameters
        const updatedParameters = { ...startNode.data.config.collected_parameters };

        // Remove all parameters associated with the deleted node
        Object.keys(updatedParameters).forEach((paramId) => {
          if (paramId.startsWith(`${nodeId}_`)) {
            console.log(`Removing parameter ${paramId} from StartNode collected parameters`);
            delete updatedParameters[paramId];
          }
        });

        // Update the StartNode's config
        setNodes((nds) =>
          nds.map((n) => {
            if (n.id === startNode.id) {
              return {
                ...n,
                data: {
                  ...n.data,
                  config: {
                    ...n.data.config,
                    collected_parameters: updatedParameters,
                  },
                },
              };
            }
            return n;
          }),
        );

        // Also update the global window.startNodeCollectedParameters
        if (typeof window !== "undefined") {
          // Use type assertion to avoid TypeScript errors
          const globalParams = (window as any).startNodeCollectedParameters || {};

          // Remove parameters for the deleted node
          Object.keys(globalParams).forEach((paramId) => {
            if (paramId.startsWith(`${nodeId}_`)) {
              delete globalParams[paramId];
            }
          });

          // Update the global object
          (window as any).startNodeCollectedParameters = globalParams;
          console.log(
            "Updated global startNodeCollectedParameters after node deletion:",
            (window as any).startNodeCollectedParameters,
          );
        }
      }

      setNodes((nds) => nds.filter((n) => n.id !== nodeId));
      setEdges((eds) => eds.filter((e) => e.source !== nodeId && e.target !== nodeId)); // Remove connected edges
      setSelectedNode(null); // Close inspector
    },
    [nodes, setNodes, setEdges],
  );

  // Track the last node data update to prevent unnecessary re-renders
  const lastNodeDataUpdateRef = useRef<{
    nodeId: string;
    dataJson: string;
    timestamp: number;
  } | null>(null);

  // Config Change Handler (e.g., for Label update)
  const handleNodeDataChange = useCallback(
    (nodeId: string, newData: WorkflowNodeData) => {
      // Check if this is a duplicate/rapid update
      const now = Date.now();
      const newDataJson = JSON.stringify(newData);
      const lastUpdate = lastNodeDataUpdateRef.current;

      // If we have a very recent update (within 50ms) with the same node and data, skip it
      if (
        lastUpdate &&
        lastUpdate.nodeId === nodeId &&
        lastUpdate.dataJson === newDataJson &&
        now - lastUpdate.timestamp < 50
      ) {
        // Skip this update as it's likely a duplicate
        return;
      }

      // Record this update
      lastNodeDataUpdateRef.current = {
        nodeId,
        dataJson: newDataJson,
        timestamp: now,
      };

      // Only log in development mode
      if (process.env.NODE_ENV === "development") {
        console.log(`handleNodeDataChange called for node ${nodeId}`);
        console.log("New data:", newData);
      }

      // Check if the node actually exists before updating
      const nodeExists = nodes.some((node) => node.id === nodeId);
      if (!nodeExists) {
        console.warn(`Attempted to update non-existent node: ${nodeId}`);
        return;
      }

      // Use a more efficient update approach that only updates the specific node
      setNodes((nds) => {
        // Find the node to update
        const nodeIndex = nds.findIndex((node) => node.id === nodeId);
        if (nodeIndex === -1) return nds; // Node not found

        // Check if the data has actually changed
        const currentNode = nds[nodeIndex];
        if (JSON.stringify(currentNode.data) === newDataJson) {
          return nds; // No change, return the same nodes array
        }

        // Create a new nodes array with the updated node
        const newNodes = [...nds];
        newNodes[nodeIndex] = {
          ...currentNode,
          data: newData,
        };

        if (process.env.NODE_ENV === "development") {
          console.log(`Updating node ${nodeId} with new data`);
        }

        return newNodes;
      });

      // Update selected node state as well if it's the one being edited
      setSelectedNode((prev) => {
        if (!prev || prev.id !== nodeId) return prev;

        // Check if the data has actually changed
        if (JSON.stringify(prev.data) === newDataJson) {
          return prev; // No change, return the same selected node
        }

        return { ...prev, data: newData };
      });
    },
    [nodes, setNodes],
  );

  // Handle keyboard shortcuts for deleting nodes
  useEffect(() => {
    const handleKeyDown = (event: globalThis.KeyboardEvent) => {
      if (
        (event.key === "Delete" || event.key === "Backspace") &&
        selectedNode &&
        !(event.target instanceof HTMLInputElement) &&
        !(event.target instanceof HTMLTextAreaElement)
      ) {
        handleDeleteNode(selectedNode.id);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedNode, handleDeleteNode]);

  // Handle copy/paste (basic implementation)
  const [copiedNode, setCopiedNode] = useState<Node<WorkflowNodeData> | null>(null);

  const handleCopyNode = useCallback(() => {
    if (selectedNode) {
      setCopiedNode(selectedNode);
    }
  }, [selectedNode]);

  const handlePasteNode = useCallback(() => {
    if (copiedNode) {
      // Use originalType if available, otherwise fall back to type
      const originalType = copiedNode.data.originalType || copiedNode.data.type;
      const newId = `${originalType}-${+new Date()}`;
      const newPosition = {
        x: copiedNode.position.x + 50,
        y: copiedNode.position.y + 50,
      };

      const newNode: Node<WorkflowNodeData> = {
        ...copiedNode,
        id: newId,
        position: newPosition,
        selected: false,
        data: { ...copiedNode.data },
      };

      setNodes((nds) => nds.concat(newNode));
    }
  }, [copiedNode, setNodes]);

  return (
    <div className="relative h-full w-full flex-grow overflow-hidden" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onPaneClick={handleCloseInspector}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        nodeOrigin={nodeOrigin}
        className="bg-background"
        snapToGrid={true}
        snapGrid={[20, 20]}
        defaultEdgeOptions={{
          animated: true,
          style: { strokeWidth: 2, stroke: "var(--primary)", zIndex: 5 },
        }}
        zoomOnScroll={true}
        zoomOnPinch={true}
        panOnScroll={true}
        elementsSelectable={true}
        selectNodesOnDrag={false}
      >
        {/* Modern controls with tooltips */}
        <Controls showInteractive={false} className="bg-card rounded-lg border p-1 shadow-md" />

        {/* Custom action panel */}
        <Panel position="top-right" className="mt-2 mr-2 flex gap-2">
          <button
            type="button"
            className="bg-card hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md border"
            onClick={handleCopyNode}
            title="Copy Node (Ctrl+C)"
          >
            <Copy className="h-4 w-4" />
          </button>

          <button
            type="button"
            className="bg-card hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md border"
            onClick={handlePasteNode}
            title="Paste Node (Ctrl+V)"
          >
            <Clipboard className="h-4 w-4" />
          </button>

          <button
            type="button"
            className="bg-card hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md border"
            onClick={() => selectedNode && handleDeleteNode(selectedNode.id)}
            disabled={!selectedNode}
            title="Delete Node (Delete)"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </Panel>

        {/* Enhanced minimap */}
        <MiniMap
          nodeStrokeWidth={3}
          zoomable
          pannable
          nodeColor={nodeColor}
          className="bg-card/80 rounded-lg border shadow-md backdrop-blur-sm"
        />

        {/* Improved background */}
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={1}
          color="var(--border)"
          className="bg-background/50"
        />
      </ReactFlow>

      {/* Inspector Panel */}
      <InspectorPanel
        selectedNode={selectedNode}
        onNodeDataChange={handleNodeDataChange}
        onClose={handleCloseInspector}
        onDeleteNode={handleDeleteNode}
        edges={edges}
        nodes={nodes}
        setIsEditingField={setIsEditingField}
      />
    </div>
  );
}

interface WorkflowCanvasWrapperProps {
  onFlowChange: (nodes: Node<WorkflowNodeData>[], edges: Edge[]) => void;
  initialWorkflow?: {
    nodes: Node<WorkflowNodeData>[];
    edges: Edge[];
  };
}

// Wrap with ReactFlowProvider is essential!
// Use React.memo to prevent unnecessary re-renders
const WorkflowCanvasWrapper = React.memo(
  function WorkflowCanvasWrapper({ onFlowChange, initialWorkflow }: WorkflowCanvasWrapperProps) {
    // Use a state to track when initialWorkflow changes
    const [key, setKey] = useState<number>(0);
    // Store the previous workflow reference to compare
    const prevWorkflowRef = useRef<typeof initialWorkflow>(null);

    // Memoize the onFlowChange callback
    const memoizedOnFlowChange = useCallback(
      (nodes: Node<WorkflowNodeData>[], edges: Edge[]) => {
        onFlowChange(nodes, edges);
      },
      [onFlowChange],
    );

    // Force re-render when initialWorkflow changes significantly
    useEffect(() => {
      if (!initialWorkflow) return;

      // Skip if this is the first render or if the workflow is the same reference
      if (!prevWorkflowRef.current || prevWorkflowRef.current === initialWorkflow) {
        prevWorkflowRef.current = initialWorkflow;
        return;
      }

      // Check if the workflow has actually changed
      const prevNodes = prevWorkflowRef.current.nodes;
      const nextNodes = initialWorkflow.nodes;
      const prevEdges = prevWorkflowRef.current.edges;
      const nextEdges = initialWorkflow.edges;

      // Only force re-render if there's a significant change
      if (prevNodes.length !== nextNodes.length || prevEdges.length !== nextEdges.length) {
        console.log("Initial workflow changed significantly, forcing re-render");
        setKey((prev) => prev + 1);
      }

      // Update the reference
      prevWorkflowRef.current = initialWorkflow;
    }, [initialWorkflow]);

    return (
      <ReactFlowProvider key={key}>
        <FlowCanvas
          onFlowChange={memoizedOnFlowChange}
          initialNodes={initialWorkflow?.nodes}
          initialEdges={initialWorkflow?.edges}
        />
      </ReactFlowProvider>
    );
  },
  // Custom comparison function for React.memo
  (prevProps, nextProps) => {
    // Only re-render if initialWorkflow has changed
    if (!prevProps.initialWorkflow && !nextProps.initialWorkflow) {
      return true; // Both are null/undefined, no need to re-render
    }

    if (!prevProps.initialWorkflow || !nextProps.initialWorkflow) {
      return false; // One is null/undefined but not the other, need to re-render
    }

    // If the references are the same, no need to re-render
    if (prevProps.initialWorkflow === nextProps.initialWorkflow) {
      return true;
    }

    // Compare nodes and edges
    const prevNodes = prevProps.initialWorkflow.nodes;
    const nextNodes = nextProps.initialWorkflow.nodes;
    const prevEdges = prevProps.initialWorkflow.edges;
    const nextEdges = nextProps.initialWorkflow.edges;

    // Simple length comparison for basic change detection
    if (prevNodes.length !== nextNodes.length || prevEdges.length !== nextEdges.length) {
      return false; // Different number of nodes or edges, need to re-render
    }

    // If we get here, assume no significant changes
    return true;
  },
);

export default WorkflowCanvasWrapper;
