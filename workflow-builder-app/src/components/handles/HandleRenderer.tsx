/**
 * HandleRenderer component for managing and rendering all handles
 */

import React, { useMemo, memo } from "react";
import { Handle, Position } from "reactflow";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import ToolHandle from "./ToolHandle";
import { isToolHandle } from "@/utils/toolConnectionUtils";
import {
  calculateHandlePositions,
  optimizeHandleLayout
} from "@/utils/handleUtils";

export interface HandleRendererProps {
  inputs: any[];
  outputs: any[];
  nodeId: string;
  isConnectable?: boolean;
  position?: "left" | "right";
  showAvailableSlots?: boolean;
  maxToolHandles?: number;
  autoSpacing?: boolean;
  enableScrolling?: boolean;
  nodeHeight?: number;
  nodeWidth?: number;
  // dynamicToolConfig removed - no longer needed with single handle approach
  onHandleConnect?: (handleId: string) => void;
  onHandleHover?: (handleId: string, isHovering: boolean) => void;
}

const HandleRenderer = memo(({
  inputs = [],
  outputs = [],
  nodeId,
  isConnectable = true,
  position = "left",
  showAvailableSlots = false,
  maxToolHandles = 10,
  autoSpacing = true,
  enableScrolling = false,
  nodeHeight = 120,
  nodeWidth = 200,
  // dynamicToolConfig removed
  onHandleConnect,
  onHandleHover,
}: HandleRendererProps) => {
  
  // Dynamic tool handle generation removed - using single handle approach
  // All inputs are now processed directly without dynamic generation
  const allInputs = useMemo(() => {
    return inputs;
  }, [inputs]);

  // Separate tool handles from regular handles
  const { toolHandles, regularHandles } = useMemo(() => {
    const tool: any[] = [];
    const regular: any[] = [];

    allInputs.forEach(input => {
      if (isToolHandle(input.name)) {
        tool.push(input);
      } else {
        regular.push(input);
      }
    });

    // Limit tool handles to maximum
    const limitedToolHandles = tool.slice(0, maxToolHandles);

    return {
      toolHandles: limitedToolHandles,
      regularHandles: regular,
    };
  }, [allInputs, maxToolHandles]);

  // Calculate positions for all handles
  const handlePositions = useMemo(() => {
    const optimizedHandles = optimizeHandleLayout([...regularHandles, ...toolHandles], {
      nodeHeight,
      nodeWidth,
      groupToolHandles: true,
      autoSpacing,
    });

    return calculateHandlePositions(optimizedHandles, {
      nodeHeight,
      nodeWidth,
      autoSpacing,
      enableScrolling,
      side: position,
    });
  }, [regularHandles, toolHandles, nodeHeight, nodeWidth, autoSpacing, enableScrolling, position]);

  // Available tool slots logic removed - not needed with single handle approach

  // Render regular handle
  const renderRegularHandle = (input: any, index: number) => {
    const positionData = handlePositions.find(p => p.name === input.name);
    const top = positionData?.position.top || (index + 1) * 30;

    return (
      <div key={`input-${input.name}`} className="relative mb-2" style={{ top }}>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="group flex items-center">
              <Handle
                type="target"
                position={Position.Left}
                id={input.name}
                data-testid={`handle-${input.name}`}
                className="regular-handle !bg-primary hover:!bg-primary/80 transition-all duration-200"
                style={{
                  width: "10px",
                  height: "10px",
                  borderRadius: "5px",
                  border: "2px solid var(--background)",
                  left: "-5px",
                  zIndex: 50,
                }}
                isConnectable={isConnectable}
              />
              <div className="overflow-hidden rounded-[5px] border border-[#3F3F46] px-[10px] py-[3px] text-[9px] font-medium text-white ml-2"
                   style={{ backgroundColor: "#1B1B1B" }}>
                {input.display_name}
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent side="left" className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm">
            <div className="font-medium">{input.display_name}</div>
            <div className="text-muted-foreground text-[10px]">
              Type: {input.input_types?.join(", ") || "any"}
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    );
  };

  // Render tool handle
  const renderToolHandle = (tool: any, index: number) => {
    return (
      <ToolHandle
        key={`tool-handle-${tool.name}`}
        id={tool.name}
        displayName={tool.display_name}
        position={position}
        isConnectable={isConnectable}
        index={index}
        totalHandles={toolHandles.length}
        onConnect={onHandleConnect}
        onHover={onHandleHover}
      />
    );
  };

  // Render available tool slot
  const renderAvailableToolSlot = (slotName: string, index: number) => {
    const top = (toolHandles.length + index + 1) * 30;

    return (
      <div 
        key={`available-${slotName}`}
        data-testid={`available-tool-slot-${slotName}`}
        className="tool-slot-available relative"
        style={{ top }}
      >
        <div className="opacity-50 border-2 border-dashed border-orange-400 rounded-md p-1 text-xs">
          Available: {slotName}
        </div>
      </div>
    );
  };

  return (
    <TooltipProvider delayDuration={150}>
      <div 
        data-testid="handles-container"
        className={`handles-container ${enableScrolling ? "handles-scrollable" : ""}`}
      >
        {/* Regular handles section */}
        <div data-testid="regular-handles-section" className="regular-handles">
          {regularHandles.map(renderRegularHandle)}
        </div>

        {/* Tool handles section */}
        <div data-testid="tool-handles-section" className="tool-handles">
          {toolHandles.map(renderToolHandle)}
        </div>

        {/* Available tool slots */}
        {showAvailableSlots && (
          <div data-testid="available-slots-section" className="available-slots">
            {availableToolSlots.map(renderAvailableToolSlot)}
          </div>
        )}

        {/* Output handles */}
        <div data-testid="output-handles-section" className="output-handles">
          {outputs.map((output, index) => (
            <div key={`output-${output.name}`} className="relative mb-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="group flex items-center justify-end">
                    <div className="overflow-hidden rounded-[5px] border border-[#3F3F46] px-[10px] py-[3px] text-[9px] font-medium text-white mr-2"
                         style={{ backgroundColor: "#1B1B1B" }}>
                      {output.display_name}
                    </div>
                    <Handle
                      type="source"
                      position={Position.Right}
                      id={output.name}
                      data-testid={`handle-${output.name}`}
                      className="regular-handle !bg-primary hover:!bg-primary/80 transition-all duration-200"
                      style={{
                        width: "10px",
                        height: "10px",
                        borderRadius: "5px",
                        border: "2px solid var(--background)",
                        right: "-5px",
                        zIndex: 50,
                      }}
                      isConnectable={isConnectable}
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right" className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm">
                  <div className="font-medium">{output.display_name}</div>
                  <div className="text-muted-foreground text-[10px]">
                    Type: {output.output_type}
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
          ))}
        </div>
      </div>
    </TooltipProvider>
  );
});

HandleRenderer.displayName = "HandleRenderer";

export default HandleRenderer;
