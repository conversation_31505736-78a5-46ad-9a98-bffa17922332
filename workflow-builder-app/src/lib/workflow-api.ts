// src/lib/workflow-api.ts
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { validateWorkflowFrontend } from "@/lib/validation/frontendValidationAdapter";

// API base URL
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

// Interface for missing field
export interface MissingField {
  nodeId: string;
  nodeName: string;
  name: string;
  displayName: string;
  info?: string;
  input_type: string;
}

// Interface for validation response
export interface ValidationResponse {
  is_valid: boolean;
  missing_fields?: MissingField[];
  error?: string;
}

// Interface for execution response
export interface ExecutionResponse {
  success: boolean;
  results?: any;
  error?: string;
  correlation_id?: string;
  save_filepath?: string;
  message?: string;
}

/**
 * Validate a workflow before execution
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @returns Validation result with missing fields if any
 */
export async function validateWorkflow(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
): Promise<ValidationResponse> {
  // Use frontend validation instead of backend API
  return validateWorkflowFrontend(nodes, edges);
}

/**
 * Execute a workflow with provided field values
 * @param nodes The workflow nodes
 * @param edges The workflow edges
 * @param fieldValues Values for missing fields, grouped by node ID
 * @returns Execution result
 */
export async function executeWorkflowWithValues(
  nodes: Node<WorkflowNodeData>[],
  edges: Edge[],
  fieldValues?: Record<string, any>,
  workflow_id?: string,
): Promise<ExecutionResponse> {
  try {
    // Find the StartNode first
    const startNode = nodes.find((node) => node.data.originalType === "StartNode");

    // Ensure the StartNode has a config object with collected_parameters
    if (startNode) {
      console.log("Found StartNode before updating:", startNode);

      // Ensure the StartNode has a config object
      if (!startNode.data.config) {
        startNode.data.config = {};
      }

      // Ensure the config has a collected_parameters object
      if (!startNode.data.config.collected_parameters) {
        startNode.data.config.collected_parameters = {};
      }

      // Ensure all parameters in collected_parameters have the required property set
      // This is critical for pre-built workflows where the required property might not be set
      if (startNode.data.config?.collected_parameters) {
        Object.keys(startNode.data.config.collected_parameters).forEach(paramId => {
          const param = startNode.data.config!.collected_parameters![paramId];
          // If required is undefined, set it to true (consider required unless explicitly false)
          if (param.required === undefined) {
            console.log(`Setting required=true for parameter ${paramId} in StartNode`);
            param.required = true;
          }
        });
      }

      console.log("StartNode config after ensuring structure:", startNode.data.config);
    } else {
      console.warn("No StartNode found in the workflow, this may cause issues");
    }

    // If fieldValues is provided, update the node configs
    if (fieldValues) {
      // Process field values to update individual nodes
      const nodeFieldValues: Record<string, Record<string, any>> = {};

      // Group field values by node ID
      Object.entries(fieldValues).forEach(([fieldId, value]) => {
        // Extract node ID and field name from the field ID
        const [nodeId, ...fieldNameParts] = fieldId.split("_");
        const fieldName = fieldNameParts.join("_");

        // Initialize node entry if it doesn't exist
        if (!nodeFieldValues[nodeId]) {
          nodeFieldValues[nodeId] = {};
        }

        // Add field value to the node
        nodeFieldValues[nodeId][fieldName] = value;
      });

      // Update nodes with their respective field values
      nodes = nodes.map((node) => {
        if (nodeFieldValues[node.id]) {
          console.log(`Updating node ${node.id} config with values:`, nodeFieldValues[node.id]);

          // Create a deep copy of the node to avoid reference issues
          const updatedNode = JSON.parse(JSON.stringify(node));

          // Ensure config exists
          if (!updatedNode.data.config) {
            updatedNode.data.config = {};
          }

          // Update the config with the new values
          Object.entries(nodeFieldValues[node.id]).forEach(([key, value]) => {
            updatedNode.data.config[key] = value;
          });

          console.log(`Node ${node.id} config after update:`, updatedNode.data.config);
          return updatedNode;
        }
        return node;
      });

      // Update the StartNode with collected parameters
      if (startNode) {
        // Create a deep copy of the collected parameters or initialize if not exists
        const collectedParameters = { ...(startNode.data.config?.collected_parameters || {}) };

        // Process each field value directly
        Object.entries(fieldValues).forEach(([fieldId, fieldValue]) => {
          // Skip if this is not a valid field ID format
          if (!fieldId.includes("_")) return;

          // Extract node ID and field name
          const [nodeId, ...fieldNameParts] = fieldId.split("_");
          const fieldName = fieldNameParts.join("_");

          // Get the node for additional metadata
          const node = nodes.find((n) => n.id === nodeId);
          if (!node) return;

          // Process value if it's a JSON string
          let processedValue = fieldValue;
          if (typeof fieldValue === "string") {
            // Check if the value looks like JSON
            if (fieldValue.trim().startsWith("{") || fieldValue.trim().startsWith("[")) {
              try {
                // Parse the JSON string to an object or array
                processedValue = JSON.parse(fieldValue);
                console.log(`Successfully parsed JSON for ${fieldName}:`, processedValue);
              } catch (e) {
                console.error(`Failed to parse JSON for ${fieldName} field:`, e);
              }
            }
          }

          // Check if this parameter is connected to the StartNode (including tool connections)
          const isConnectedToStartNode = edges.some((edge) => {
            // Check if this edge connects the StartNode to this node
            const isFromStartNode = edge.source === startNode.id;
            const isToThisNode = edge.target === nodeId;

            // Check if the target handle matches this field
            let matchesField = false;
            if (edge.targetHandle) {
              // Handle different formats of target handles
              if (edge.targetHandle === fieldName) {
                matchesField = true;
              } else if (edge.targetHandle === `input_${fieldName}`) {
                matchesField = true;
              } else if (edge.targetHandle.includes(fieldName)) {
                matchesField = true;
              }
            }

            return isFromStartNode && isToThisNode && matchesField;
          });

          // Check if this node is connected as a tool to any AgenticAI node
          const isConnectedAsTool = edges.some((edge) => {
            return edge.source === nodeId && edge.targetHandle && /^tool_\d+$/.test(edge.targetHandle);
          });

          // Store the parameter with full metadata
          collectedParameters[fieldId] = {
            node_id: nodeId,
            node_name: node.data.label || "Unknown Node",
            input_name: fieldName,
            value: processedValue,
            connected_to_start: isConnectedToStartNode,
            connected_as_tool: isConnectedAsTool,
          };
        });

        // Update the StartNode config
        startNode.data.config = {
          ...startNode.data.config,
          collected_parameters: collectedParameters,
        };

        console.log("StartNode config after updating collected parameters:", startNode.data.config);

        // Also update the global window.startNodeCollectedParameters for future reference
        if (typeof window !== "undefined") {
          // Use type assertion to avoid TypeScript errors
          (window as any).startNodeCollectedParameters = startNode.data.config.collected_parameters;
          console.log(
            "Updated global startNodeCollectedParameters:",
            (window as any).startNodeCollectedParameters,
          );
        }
      }
    }

    // Filter out nodes that are not connected to the StartNode (including tool connections)
    let filteredNodes = nodes;
    let filteredEdges = edges;

    if (startNode) {
      console.log("[WORKFLOW EXECUTE] Filtering out unconnected nodes before execution (including tool connections)");

      // Import the enhanced getConnectedNodes function that includes tool connections
      const { getConnectedNodesWithToolConnections } = await import("@/lib/validation/toolConnectionFlow");

      // Find all nodes connected to the StartNode including tool connections
      const connectedNodes = getConnectedNodesWithToolConnections(nodes, edges, startNode.id);
      console.log(`[WORKFLOW EXECUTE] Found ${connectedNodes.size} nodes connected to StartNode (including tool connections): ${Array.from(connectedNodes).join(', ')}`);

      // Check if there are any disconnected nodes
      const disconnectedNodesCount = nodes.length - connectedNodes.size;
      if (disconnectedNodesCount > 0) {
        console.log(`[WORKFLOW EXECUTE] Found ${disconnectedNodesCount} disconnected nodes that will be excluded from execution`);

        // Filter nodes to include only those connected to the StartNode
        filteredNodes = nodes.filter(node => connectedNodes.has(node.id));

        // Filter edges to include only those connecting filtered nodes
        filteredEdges = edges.filter(edge =>
          connectedNodes.has(edge.source) && connectedNodes.has(edge.target)
        );

        console.log(`[WORKFLOW EXECUTE] Filtered workflow contains ${filteredNodes.length} nodes and ${filteredEdges.length} edges`);

        // Import toast dynamically to avoid server-side rendering issues
        try {
          // Use dynamic import for toast
          const { toast } = await import("sonner");

          // Show a warning toast notification
          toast.warning(
            `${disconnectedNodesCount} unconnected ${disconnectedNodesCount === 1 ? 'node has' : 'nodes have'} been excluded from execution.`,
            {
              description: "Only nodes connected to the Start node (including tool connections) are included in the workflow execution.",
              duration: 5000,
            }
          );
        } catch (error) {
          console.error("Failed to show toast notification:", error);
        }
      }
    }

    // CRITICAL FIX: Generate tool connections from edges instead of config
    // This ensures tool nodes remain visible and editable on the canvas
    if (startNode && fieldValues) {
      const collectedParameters = { ...(startNode.data.config?.collected_parameters || {}) };

      // Generate tool connections for AgenticAI nodes from visual edges
      const agenticAINodes = filteredNodes.filter(node => node.data.originalType === "AgenticAI");
      agenticAINodes.forEach(agenticNode => {
        // Find all edges connecting to this AgenticAI node's tool handles
        const toolEdges = filteredEdges.filter(edge =>
          edge.target === agenticNode.id &&
          edge.targetHandle &&
          (edge.targetHandle.includes("tool") || edge.targetHandle === "tools")
        );

        if (toolEdges.length > 0) {
          console.log(`[WORKFLOW EXECUTE] Found ${toolEdges.length} tool edges for AgenticAI node ${agenticNode.id}`);

          // Create simplified tool connections array (no handle grouping)
          const tools: any[] = [];

          toolEdges.forEach(edge => {
            const sourceNode = filteredNodes.find(node => node.id === edge.source);
            if (sourceNode) {
              // Create tool connection data from the source node
              const toolConnection = {
                node_id: sourceNode.id,
                node_type: sourceNode.data.originalType || sourceNode.data.type || "Unknown",
                node_label: sourceNode.data.label || sourceNode.data.definition?.display_name || "Unknown Tool",
                component_id: sourceNode.id,
                component_type: sourceNode.data.originalType || sourceNode.data.type || "Unknown",
                component_name: sourceNode.data.label || sourceNode.data.definition?.display_name || "Unknown Tool",
                component_definition: sourceNode.data.definition || {},
                component_config: sourceNode.data.config || {},
                // Add MCP metadata if available
                ...(sourceNode.data.definition?.mcp_info && {
                  mcp_metadata: sourceNode.data.definition.mcp_info
                })
              };

              tools.push(toolConnection);
              console.log(`[WORKFLOW EXECUTE] Added tool connection: ${sourceNode.data.label} -> ${agenticNode.data.label}`);
            }
          });

          // Update the AgenticAI node's config with simplified tool connections
          agenticNode.data.config = {
            ...agenticNode.data.config,
            tools: tools  // Simplified: config.tools instead of config.tool_connections
          };

          console.log(`[WORKFLOW EXECUTE] Generated simplified tool connections for AgenticAI node ${agenticNode.id}:`, tools);
        }
      });

      // Update the StartNode config with tool connection data
      startNode.data.config = {
        ...startNode.data.config,
        collected_parameters: collectedParameters,
      };
    }

    // Log the node configs to help with debugging
    console.log("Executing workflow with filtered nodes:", filteredNodes);
    console.log("StartNode config before execution:", startNode?.data.config);

    // Log specific node configs that might have updated values
    filteredNodes.forEach((node) => {
      if (node.data.originalType === "ScriptGenerateNode" || node.id.includes("generate-script")) {
        console.log(`Script Generate Node ${node.id} config:`, node.data.config);
      }
    });

    const response = await fetch(`${API_BASE_URL}/execute`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ nodes: filteredNodes, edges: filteredEdges, workflow_id }),
    });

    if (!response.ok) {
      throw new Error(`Error executing workflow: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error executing workflow:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
